<template>
  <div class="innerMid animate__animated animate__fadeIn">
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: "centerWrapBox",
  data() {
    return {}
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
.innerMid {
  width: 3426px;
  height: 1927px;
  // background-color: antiquewhite;
  background: url('~@/assets/application/midBgTwo.png') no-repeat center center;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 50px 108px 20px 108px;
}
</style>