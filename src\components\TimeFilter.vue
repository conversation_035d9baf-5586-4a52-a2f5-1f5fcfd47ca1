<template>
  <div class="timeFilter">
    <div class="labelWrap">历史数据查询：</div>
    <div class="monthSelectWrap">
      <el-select v-model="yearTime" placeholder="请选择" size="large">
        <el-option v-for="item in yearOptions" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
    </div>
    <div class="labelWrap yy">年</div>
    <div class="monthSelectWrap">
      <el-select v-model="monthTime" placeholder="请选择" size="large">
        <el-option v-for="item in monthOptions" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
    </div>
    <div class="labelWrap mm">月</div>
    <div class="cxbtnWrap">查询</div>
    <div class="czbtnWrap">重置</div>
  </div>
</template>

<script>
export default {
  model: {
    prop: "childVal",
    event: "changeParentVal"
  },
  props: {
    childVal: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      yearTime: '',
      monthTime: '',
      monthOptions: [{
        value: '01',
        label: '01'
      }, {
        value: '02',
        label: '02'
      }, {
        value: '03',
        label: '03'
      }, {
        value: '04',
        label: '04'
      }, {
        value: '05',
        label: '05'
      }, {
        value: '06',
        label: '06'
      }, {
        value: '07',
        label: '07'
      }, {
        value: '08',
        label: '08'
      }, {
        value: '09',
        label: '09'
      }, {
        value: '10',
        label: '10'
      }, {
        value: '11',
        label: '11'
      }, {
        value: '12',
        label: '12'
      }],
      yearOptions: [{
        value: '2023',
        label: '2023'
      }, {
        value: '2022',
        label: '2022'
      }, {
        value: '2021',
        label: '2021'
      }, {
        value: '2020',
        label: '2020'
      }, {
        value: '2019',
        label: '2019'
      }, {
        value: '2018',
        label: '2018'
      }, {
        value: '2017',
        label: '2017'
      }, {
        value: '2016',
        label: '2016'
      }, {
        value: '2015',
        label: '2015'
      }, {
        value: '2014',
        label: '2014'
      }, {
        value: '2013',
        label: '2013'
      }, {
        value: '2012',
        label: '2012'
      }, {
        value: '2011',
        label: '2011'
      }, {
        value: '2010',
        label: '2010'
      }, {
        value: '2009',
        label: '2009'
      }]
    }
  },
  created () {
    const d = new Date()
    this.yearTime = d.getFullYear()
    this.monthTime = d.getMonth() + 1

  },
  methods: {},
  computed: {
    transitionMethod () {
      // console.log('computed值发生了变化', `${this.yearTime}.${this.monthTime}`)
      return `${this.yearTime}.${this.monthTime}`
    }
  },
  watch: {  // 在这里对 transitionMethod 进行使用，不然不执行 transitionMethod
    transitionMethod: {
      handler (newVal, oldVal) {
        this.$emit("changeParentVal", `${this.yearTime}.${this.monthTime}`)
        // console.log('transitionMethod的值', newVal, oldVal)
      }
    }
  }
}
</script>

<style scoped lang="less">
.timeFilter {
  display: flex;
  align-items: center;

  ::v-deep .el-input__inner {
    background-color: #132C4E;
    border: 1px solid #337FC6;
    color: #FEFEFE;
    font-size: 24px;
    height: 54px;

  }

  ::v-deep .el-select__caret {
    color: #FEFEFE;
    // font-size: 24px;
    // margin-bottom: 20px;

  }

  ::v-deep .el-input__inner::placeholder {
    color: #fff;
  }

  .labelWrap {
    font-size: 36px;
    color: #FEFEFE;
    // margin-bottom: 10px;

  }

  .yy {
    margin: 0 20px;
  }

  .mm {
    margin-left: 21px;
  }

  .monthSelectWrap {
    width: 170px;
    height: 54px;
  }

  .cxbtnWrap {
    width: 120px;
    height: 54px;
    background: linear-gradient(0deg, #148AF0, #0984F1, #75B9EE, #75B9EE);
    border-radius: 6px;
    font-size: 36px;
    color: #FEFEFE;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin-right: 16px;
    margin-left: 35px;
  }

  .czbtnWrap {
    width: 120px;
    height: 54px;
    background: rgba(19, 44, 78, 0.9);
    border: 1px solid;
    border-image: linear-gradient(-32deg, #359CF8, #AFDCFB) 1 1;
    border-radius: 6px;
    font-size: 36px;
    color: #FEFEFE;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
}
</style>