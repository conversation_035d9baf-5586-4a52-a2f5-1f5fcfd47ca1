.mapPopup {
  position: fixed;
  z-index: 1;
  min-width: fit-content !important;
  min-height: fit-content !important;
  background: url("../img/hoverBg.png") no-repeat !important;
  background-size: cover !important;
  /*box-shadow: inset 0px 0px 16px 0px rgba(54, 162, 237, 0.62);*/

  transform-origin: 50% 100%;
  transition: transform 0.3s ease-out;
}

.mapContainer .mapPopup {
  max-width: fit-content !important;
  max-height: fit-content !important;
  border-radius: 0 !important;
}

.hoverContainer {
  width: 100%;
  height: fit-content;
  margin: 14px 64px 14px 14px;
}

.hoverContainer-item {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 17px;
}

.key {
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 28px;
  color: #00F0FF;
}

.value {
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 28px;
  color: #FFFFFF;
  margin-left: 14px;
}

.show {
  transform: translate(-50%, calc(-100% - 16px)) scale(1);
}

.hide {
  transform: translate(-50%, calc(-100% - 16px)) scale(0);
}
.mapPopup .header {
  height: 0 !important;
  padding: 0 16px;
  display: flex !important;
  justify-content: flex-end;
  align-items: center;
  font-size: 18px;
  position: relative;
}

#mapPopupTitle {
  display: none;
}

.closeBtn {
  font-size: 25px !important;
  position: fixed;
  right: 10px;
  top: 10px;
  z-index: 99;
}

.mapPopup .header::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    rgba(0, 151, 255, 0) 0%,
    #0097ff 25%,
    rgba(0, 151, 255, 0) 100%
  );
}
.mapPopup .header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, #00ffff 0%, #0097ff 100%);
}
.mapPopup .closeBtn {
  flex: 0 0 20px;
  width: 20px;
  height: 20px;
  color: rgba(255, 255, 255, 0.65);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.mapPopup .closeBtn {
  font-size: 16px;
}
.mapPopup .closeBtn:hover {
  color: rgba(255, 194, 16, 1);
}

.mapPopup .body {
  position: relative;
}

.mapPopup .body::before {
  content: "";
  display: block;
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
  width: 20px;
  height: 20px;
  background: rgba(24, 77, 130, 0.8);
  clip-path: polygon(0 100%, 100% 0, 100% 100%);
  box-shadow: 0px 2px 12px 0px rgba(3, 11, 28, 0.08);
}

.mapPopup .bodyContent {
  z-index: 1;
  padding: 20px;
}
.mapPopup .bodyContent .item {
  margin-bottom: 16px;
  display: flex;
}
.mapPopup .bodyContent .item span:nth-child(1) {
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular, AlibabaPuHuiTi;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.85);
  line-height: 20px;
}
.mapPopup .bodyContent .item span:nth-child(2) {
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Medium, AlibabaPuHuiTi;
  font-weight: 400;
  color: #ffffff;
  line-height: 20px;

  white-space: nowrap; /*瑙勫畾娈佃惤涓殑鏂囨湰涓嶈繘琛屾崲琛� */
  overflow: hidden; /*瓒呭嚭閮ㄥ垎闅愯棌*/
  text-overflow: ellipsis; /* 瓒呭嚭閮ㄥ垎鏄剧ず鐪佺暐鍙� */
}

.mapPopup .title {
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Medium, AlibabaPuHuiTi;
  font-weight: 500;
  color: #ffffff;
  line-height: 22px;

  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.mapPopup .pageContainer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  color: #ffffff;
  padding-right: 20px;
}

.mapPopup .esri-popup__icon {
  cursor: pointer;
}