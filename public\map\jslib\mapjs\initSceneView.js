/*
 * @Descripttion:
 * @Author: <EMAIL>
 * @Date: 2022-12-11 17:54:19
 */
import Map from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.26/@arcgis/core/Map.js";
import SceneView from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.26/@arcgis/core/views/SceneView.js";
import Basemap from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.26/@arcgis/core/Basemap.js";
import { layerCreate } from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/core.js";
import {
  getSzLightBasemap,
  getTDTCGCS2000DarkLayer,
  getTDTCGCS2000SYLayer,
  getTDTCGCS2000ImageLayer,
  getKaiFaQuBasemap,
  getCustomBasemp,
  get2023ImageLayer,
} from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/basemap.js";
// import layerCreatAsync from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/layerCreatAsync.js";
import { getLayerConfigById } from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/layerConfig.js";
import addWaterWaveEffect from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/waterWaveEffect.js";
import esriConfig from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.26/@arcgis/core/config.js";
import MapClickEventHandle from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/MapClickEventHandle.js";
import MapPopupWidget from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/MapPopupWidget.js";
import { vectorWaterColor, imageWaterColor, memoryLimit } from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/config.js";
import { addDEMToMap, removeDEMFromMap, addPoiLayers } from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/exchangeMap.js";

const defaultCamera = {
  position: {
    spatialReference: {
      wkid: 4490,
    },
    x: 119.65842342884746,
    y: 28.97890877935061,
    z: 10280.48295974452,
  },
  heading: 354.2661149152386,
  tilt: 47.902020858006175,
};
const defaultGlobalCamera = {
  position: {
    spatialReference: {
      latestWkid: 4490,
      wkid: 4490,
    },
    x: 120.12656231499913,
    y: 28.154407308672614,
    z: 25512548.000000015,
  },
  heading: 354.9940451770809,
  tilt: 0.09998686595828937,
};
/**
 * 加载SceneView
 * @param {*} urlTemplate
 * @param {*} divId
 */
function initSceneView({
  divId = "viewDiv",
  camera = defaultCamera,
  basemap = "TDT_vct",
  viewingModeExtend = "global",
  isDefaultGoToFromGlobal = false,
  isDefaultAddDem = true,
  alphaCompositingEnabled = false,
  environment,
  ground = {
    opacity: 1,
    surfaceColor: "#08294a",
  },
  token,
}) {
  const baseMapConfig = {
    vector: getSzLightBasemap,
    image: getTDTCGCS2000SYLayer,
    TDT_vct: getTDTCGCS2000DarkLayer,
    TDT_img: getTDTCGCS2000ImageLayer,
    KAIFAQU: getKaiFaQuBasemap,
    image2023: get2023ImageLayer,
  };
  const type = typeof basemap;
  let catchBaseMap = null;
  if (type === "object") {
    if (!basemap.title || !basemap.layerConfigs) {
      throw Error("底图title和layerConfigs必传");
    }

    catchBaseMap = getCustomBasemp(basemap);
  } else if (type === "string") {
    catchBaseMap = baseMapConfig[basemap]();
  } else {
    throw Error("暂不支持该底图类型");
  }

  const initBaseMapCfg = getLayerConfigById("initBaseLayer"); //初始化三维场景全球底图
  const lyr = layerCreate({
    ...initBaseMapCfg,
  });

  const baseMap = new Basemap({
    baseLayers: [lyr],
  });

  const map = new Map({
    basemap: isDefaultGoToFromGlobal ? baseMap : catchBaseMap,
    ground,
  });
  const viewProps = {
    container: divId,
    alphaCompositingEnabled,
    map: map,
    camera: isDefaultGoToFromGlobal ? defaultGlobalCamera : camera,
    constraints: {
      altitude: {
        min: 10,
      },
    },
  };
  environment && (viewProps.environment = environment);
  if (viewingModeExtend === "global" || viewingModeExtend === "local") {
    viewProps.viewingMode = viewingModeExtend;
  } else if (typeof viewingModeExtend == "object") {
    viewProps.camera = viewingModeExtend;
  }
  const view = new SceneView(viewProps);
  view.qualityProfile = "low";
  view.qualitySettings.memoryLimit = memoryLimit;
  view.popup.autoOpenEnabled = false;
  view.when(() => {
    //添加poi
    switch (basemap) {
      case "vector":
        addPoiLayers({ view, color: [255, 255, 255], size: 22 });
        break;
      case "image":
        addPoiLayers({ view, color: [0, 153, 255], size: 22 });
        break;
      case "image2023":
        addPoiLayers({ view, color: [0, 153, 255], size: 22 });
        break;
      default:
        break;
    }

    if (isDefaultGoToFromGlobal) {
      setTimeout(() => {
        view.goTo(camera, {
          speedFactor: 0.8,
        });
      }, 4000);
      view.watch("zoom", (e) => {
        //zoom大于9级时清除
        if (e > 9) {
          if (
            view.map.basemap.baseLayers.items.some(
              (layer) => layer.id === "initBaseLayer"
            )
          ) {
            baseMap.destroy();
            view.map.basemap = catchBaseMap;
          }
        }
      });
    }

  });

  window.view = view;
  isDefaultAddDem && addDEMToMap(view);
  // goHome方法
  view.goHome = () => {
    view.goTo(camera);
  };

  let waterColor = imageWaterColor;
  if (basemap === "vector" || basemap == "TDT_vct") {
    waterColor = vectorWaterColor;
  }
  //去掉水波纹
  // if (basemap != "image") {
  //   addWaterWaveEffect({
  //     color: waterColor,
  //     zoom: 15.422761891002374,
  //   });
  // }

  // 可以访问wmts服务
  const regExp = new RegExp("/geoserver/JHRS/");
  esriConfig.request.interceptors.push({
    urls: regExp,
    before: function (param) {
      let urlSplit = param.url.split("/geoserver/JHRS/");
      const newUrl = `${window.location.protocol}//csdn.dsjj.jinhua.gov.cn:8101/geoserver/JHRS/${urlSplit[1]}`;
      param.url = newUrl;
    },
  });

  esriConfig.request.interceptors.push({
    urls: "https://csdnwlgz.dsjj.jinhua.gov.cn:82/map/rest/services",
    before: function (params) {
      if (params.requestOptions.query) {
        params.requestOptions.query.gctk =
          token || "727d5ddcc1ae4d1f9f5aaccab7100a54";
      } else {
        params.requestOptions.query = {
          gctk: token || "727d5ddcc1ae4d1f9f5aaccab7100a54",
        };
      }
    },
  });

  let mapClickEventHandle = new MapClickEventHandle(view);
  if (ArcGisUtils) {
    ArcGisUtils.mapClickEventHandle = mapClickEventHandle;
    ArcGisUtils.mapPopupWidget = new MapPopupWidget({
      view,
    });
  }

  return view;
}

export default initSceneView;
