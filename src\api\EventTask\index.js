import {request} from '@/utils/request'

// 任务完成情况表格
export function getTaskList() {
  return request({
    url: '/ywtg-api/kfqywtg/task/screenlist',
    method: 'get'
  })
}

// 任务详细信息
export function getTaskDetail(id) {
  return request({
    url: `/ywtg-api/kfqywtg/task/${id}`,
    method: 'get'
  })
}

// 任务详细信息
export function updateTask(data) {
  return request({
    url: `/ywtg-api/kfqywtg/task`,
    method: 'put',
    data
  })
}

// 重大任务列表
export function getZdrwList(params) {
  return request({
    url: '/ywtg-api/kfqywtg/task/screenzdrwlist',
    method: 'get',
    params
  })
}

// 新增事件管理-任务库
export function addTask(data) {
  return request({
    url: '/ywtg-api/kfqywtg/task',
    method: 'post',
    data: data
  })
}

// 跟踪督办-统计分析
export function getTjfxChart() {
  return request({
    url: '/ywtg-api/kfqywtg/task/screenStatic',
    method: 'get'
  })
}

// 跟踪督办-统计分析
export function getTaskConnectChart() {
  return request({
    url: '/ywtg-api/kfqywtg/task/screenRwjrqk',
    method: 'get'
  })
}

// 任务排名
export function getTaskTop(params) {
  return request({
    url: '/ywtg-api/kfqywtg/task/screenRwpm',
    method: 'get',
    params
  })
}

// 任务趋势
export function getTaskProgress(params) {
  return request({
    url: '/ywtg-api/kfqywtg/task/screenRwqs',
    method: 'get',
    params
  })
}

// 任务数量
export function getTaskNumber(params) {
  return request({
    url: '/ywtg-api/kfqywtg/task/screenRwsl',
    method: 'get',
    params
  })
}

// 事件趋势分析
export function getEventQs(params) {
  return request({
    url: '/ywtg-api/kfqywtg/event/screenSjqsfx',
    method: 'get',
    params
  })
}

// 事件组成
export function getEventZc(params) {
  return request({
    url: '/ywtg-api/kfqywtg/event/screenSjzc',
    method: 'get',
    params
  })
}

// 事件处置情况
export function getEventCzqk(params) {
  return request({
    url: '/ywtg-api/kfqywtg/event/screenSjczqk',
    method: 'get',
    params
  })
}

// 今日事件
export function getEventCurrentDate() {
  return request({
    url: '/ywtg-api/kfqywtg/event/screensjtop20',
    method: 'get'
  })
}

// 责任部门事件处置分析
export function getEventCzfx(params) {
  return request({
    url: '/ywtg-api/kfqywtg/event/screenZrbmsjczfx',
    method: 'get',
    params
  })
}

// 事件类型分析
export function getEventTypefx() {
  return request({
    url: '/ywtg-api/kfqywtg/event/screenSjlxfx',
    method: 'get'
  })
}

// 事件数量
export function getEventNumber(params) {
  return request({
    url: '/ywtg-api/kfqywtg/event/screenSjsl',
    method: 'get',
    params
  })
}

// 事件详情
export function getEventDetail(id) {
  return request({
    url: `/ywtg-api/kfqywtg/event/screenSjxq/${id}`,
    method: 'get'
  })
}

// 根据分类获取事件点位
export function getEventPointList(params) {
  return request({
    url: '/ywtg-api/kfqywtg/event/screenhqdw',
    method: 'get',
    params
  })
}
