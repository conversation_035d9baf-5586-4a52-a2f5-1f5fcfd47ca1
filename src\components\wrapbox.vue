<template>
  <div class="wrapbox" :class="{ leftbg: bg === 'left', rightbg: bg === 'right' }" :style="{width: width + 'px'}">
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'wrapbox',
  data() {
    return {}
  },
  props: {
    //left左侧背景图,right右侧背景图
    bg: {
      type: String,
      default: 'left',
    },
    width:{
      type: Number,
      default: 2097
    }
  },
  computed: {},
  mounted() {},
  methods: {},
  watch: {},
}
</script>

<style scoped>
.wrapbox {
  height: 1927px;
  background: linear-gradient(180deg, #0e1a40, #064069);
}

.leftbg {
  background: url('../assets/common/left-bg.png') no-repeat;
  background-size: cover;
}

.rightbg {
  background: url('../assets/common/right-bg.png') no-repeat;
  background-size: cover;
}
</style>