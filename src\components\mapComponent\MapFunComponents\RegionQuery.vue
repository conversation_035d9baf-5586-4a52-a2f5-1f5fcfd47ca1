<template>
  <div class="contentmain quyuchaxun"  >
    <!-- 功能按钮--------------------------------------------- -->
    <div style="padding: 30px">
      <div style="margin-bottom: 45px" id="area-box">
        <div class="items-title">
          <span>框选查询</span>
          <div :id="typeID==0&&'active'" @click="measureFun('框选查询')">{{typeID===0?'关闭':'使用'}}</div>
        </div>
        <div id="areaTool"></div>
      </div>
      <div style="margin-bottom: 45px" id="angle-box">
        <div class="items-title">
          <span>绘圆查询</span>
          <div :id="typeID==1&&'active'" @click="measureFun('绘圆查询')">{{typeID===1?'关闭':'使用'}}</div>
        </div>
        <div id="angleTool"></div>
      </div>
      <div style="margin-bottom: 45px" id="dis-box">
        <div class="items-title">
          <span>点选查询</span>
          <div :id="typeID==2&&'active'" @click="measureFun('点选查询')">{{typeID===2?'关闭':'使用'}}</div>
        </div>
        <div id="disTool"></div>
      </div>
    </div>
    <!-- 结果展示--------------------------------------------- -->
    <div>
      <div id="circle_sou" class="s-flex" v-show="typeID==1">
        <div :class="circle_center.length>0?'icon-nav-start-active':'icon-nav-start'" @click="getStart">点</div>
        <el-slider style="width: 425px" v-model="circle_r" :step="250" :max="2000" :min="250" show-input show-stops
          @change="getRadius"></el-slider>
      </div>
      <div id="point_sou" v-show="typeID==2">
        <el-select v-model="queryValue" placeholder="选择要素图层" @visible-change="layersCheck()">
          <el-option v-for="item in queryLyrs" :key="item.value" :label="item.label" :value="item.value"
            :disabled="item.disabled"></el-option>
        </el-select>
      </div>
      <div id="result">
        <p class="hearder_h2 s-m-b-10">
          <img src="@/assets/map/tit-l.png" width="180px" alt="" />
          <span>区域情况</span>
          <img src="@/assets/map/tit-r.png" width="180px" alt="" />
        </p>
        <div class="s-flex s-row-center s-font-26 s-c-white s-text-center">
          <p class="s-m-10">区域人数</p>
          <div class="s-flex">
            <div class="number s-c-yellow-gradient" v-for="(item, i) in count" :key="i">
              <span class="numbg" v-if="item!=','&&item!='.'">
                <count-to :start-val="0" :end-val="Number(item)" :duration="3000"
                  class="s-c-yellow-gradient"></count-to>
              </span>
              <span v-else>{{item}}</span>
            </div>
            人
          </div>
        </div>
        <div class="custom-table">
          <div class="table-container">
            <div class="table-row" v-for="(row, index) in tableData" :key="index">
              <div class="table-cell name-cell">
                <div class="s-flex">
                  <img :src="require('@/assets/map/'+row.icon+'.png')" alt="" width="30px" />
                  <span class="s-c-blue-gradient1">{{row.name}}</span>
                </div>
              </div>
              <div class="table-cell">{{row.num}}{{row.unit}}</div>
              <div class="table-cell">{{row.avg}}{{row.unit}}/每千人</div>
              <div class="table-cell checkbox-cell">
                <input type="checkbox" 
                  :checked="clickName.includes(row)"
                  @change="handleCheckboxChange($event, row)"
                  class="custom-checkbox"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 全局变量声明，这些应该在项目中已经定义
const baseURL = window.baseURL || { url: '' }
const axios = window.axios || require('axios')
const simplify = window.simplify
const turf = window.turf
const $api = window.$api || function() { console.warn('$api not available') }

// 引入count-to组件
import CountTo from 'vue-count-to'

export default {
  name: 'RegionQuery',
  components: {
    CountTo
  },
  data() {
    return {
      typeID: null, //0=框选查询 1=绘圆查询 2=点选查询
      count: '00000',
      clickName: [],
      tableData: [
        {
          name: '医疗机构',
          code: '医院',
          codenum: '170101,170102,170103,170104,170105',
          icon: '医院c',
          num: '',
          unit: '家',
          avg: '',
          arr: [],
        },
        {
          name: '学校',
          icon: '学校c',
          num: '',
          code: '学校',
          codenum: '160101, 160102,  160103, 160104,  160105, 160106,  160107',
          unit: '所',
          avg: '',
          arr: [],
        },
        {
          name: '公交车站',
          code: '公交站',
          codenum: '230105',
          icon: '公交车站c',
          num: '',
          unit: '个',
          avg: '',
          arr: [],
        },
        {
          name: '公共厕所',
          code: '公厕',
          codenum: '210102',
          icon: '公共厕所c',
          num: '',
          unit: '个',
          avg: '',
          arr: [],
        },
        {
          name: '运动场馆',
          code: '游泳场池,高尔夫球场,场馆',
          codenum: '180101,180105,180106',
          icon: '运动场馆c',
          num: '',
          unit: '个',
          avg: '',
          arr: [],
        },
        {
          name: '公园广场',
          code: '植物园,公园,广场,动物园',
          codenum: '180303,180307,180402,180404,180405',
          icon: '公园广场c',
          num: '',
          unit: '个',
          avg: '',
          arr: [],
        },
      ],

      circle_r: 0,
      circle_center: [],
      circle_inhead: true,

      queryValue: '',
      queryLyrs: [],
      nameId: {},
      foreClickPolyCode: null,
      distance: 0,
      centroid: [],
    }
  },
  mounted() {
    console.log('Component mounted, initial count:', this.count)
    this.creditAuth()
  },
  methods: {
    initMap() {
      console.log('initMap called')
      this.circle_center = []
      this.circle_inhead = true
      this.count = '00000'
      console.log('count after initMap:', this.count)
      this.tableData.map((res) => {
        res.num = ''
        res.avg = ''
        res.arr = []
        top.mapUtil.removeLayer('personCount' + res.name)
      }) //清除poi点位

      // this.$refs.multipleTable.clearSelection() //清除复选框

      top.mapUtil.removeAllLayers([
        'circle_draw_centerpoint',
        'circle_draw_centerRadius',
        'circle_draw_centerLine',
        'circle_draw',
        'drawMine',
        'video-point',
      ])
      top.mapUtil.removeLayer('syr') //视频详情弹窗
      top.mapUtil.removeLayer('syr1') //视频详情弹窗
      top.mapUtil.tool.stopPick() // 停止获取鼠标点位
      top.mapUtil.plotTool.close() //关闭画圆
      top.mapUtil.drawTool.clear()
      if (window.clickgra) window.clickgra.remove()
    },
    measureFun(typeKey) {
      if (this.typeID != typeKey) this.initMap()
      let funObj = {
        框选查询: () => {
          this.typeID == 0 ? (this.typeID = null) : (this.typeID = 0)
          top.mapUtil.plotTool.active('polygon', (response) => {
            console.log('框选查询-', response)
            this.createWLGZ(response)
          })
        },
        绘圆查询: () => {
          this.typeID == 1 ? (this.typeID = null) : (this.typeID = 1)
          this.drawC()
        },
        点选查询: () => {
          this.typeID == 2 ? (this.typeID = null) : (this.typeID = 2)
        },
      }
      funObj[typeKey]()
      if (this.typeID == null) {
        this.initMap()
        return
      }
    },
    isHas(small, big) {
      let main = []
      if (small.length == 0) return big[0]
      for (let i = 0; i < small.length; i++) {
        big.filter((ele) => {
          if (ele != small[i]) {
            main = ele
          }
        })
      }
      return main
    },
    handleSelectionChange(e) {
      if (this.clickName.length > e.length) {
        //减
        // let item = this.isHas(e, this.clickName) //赛选减的目标
        top.mapUtil.removeLayer('personCount' + item.name)
      } else {
        //加
        let mainObj = this.isHas(this.clickName, e) //赛选减的目标
        let pointData = []
        mainObj.arr.map((item) => {
          let str = {
            lng: item.x,
            lat: item.y,
            name: item.name,
            address: item.address || item.city + item.county + item.town,
          }
          if (item.x != '' && item.x != null && item.x != undefined) {
            pointData.push(str)
          }
        })
        let pointId = 'personCount' + mainObj.name
        this.pointTextMapFun(mainObj.icon, pointData, pointId, 1)
      }
      this.clickName = e
    },
    pointTextMapFun(icon, pointData, pointId, iconSize) {
      let popcfg = {
        offset: [50, -100],
        show: true,
        dict: { name: '名称', address: '地址' },
      }
      top.mapUtil.loadPointLayer({
        data: pointData,
        layerid: pointId, //图层id
        iconcfg: { image: icon, iconSize: iconSize }, //图标
        popcfg: popcfg,
      })
    },
    // 绘圆查询
    drawC() {
      if (this.circle_inhead && this.circle_center.length == 0) {
        top.mapUtil.plotTool.active('circle', (response) => {
          this.createWLGZ({
            type: 'Feature',
            geometry: {
              coordinates: response.coordinates,
              type: 'MultiPolygon',
            },
            properties: {
              area: parseInt(response.radius * 1000) || 0,
              center: response.center,
              w_cen: response.coordinates[0][0],
              type: 'circle_my',
            },
          })
        })
      }
    },
    getStart() {
      if (this.circle_inhead == false) return
      this.initMap()
      this.circle_inhead = false
      top.mapUtil.tool.pickPoint((e) => {
        this.circle_center = [e.lng, e.lat]
        this.mapCenPoint(e)
        this.getRadius(this.circle_r)
        top.mapUtil.tool.stopPick() // 停止获取鼠标点位
      })
    },
    getRadius(val) {
      if (this.circle_inhead == true) return
      top.mapUtil.drawTool.draw('circle', {
        circles: [
          {
            center: this.circle_center,
            radius: val,
            fillColor: [255, 255, 255, 0.1],
            strokeColor: [255, 255, 255, 1],
          },
        ],
        layerid: 'circle_draw',
      })
      let geogson = top.mapUtil.drawTool.layers.circle_draw.subs[0].geometry.rings
      this.createWLGZ({
        type: 'Feature',
        geometry: {
          coordinates: geogson,
          type: 'MultiPolygon',
        },
        properties: {
          area: Number(val) || 0,
          center: this.circle_center,
          w_cen: geogson[0][0],
          type: 'circle',
        },
      })
    },
    drawR(properties) {
      if (this.circle_center.length == 0) {
        this.mapCenPoint(properties.center)
      }
      this.mapLine(properties)
      this.mapText(properties)
    },
    mapCenPoint(e) {
      top.mapUtil.removeAllLayers(['circle_draw_centerpoint'])
      top.mapUtil.loadPointLayer({
        data: [e],
        layerid: 'circle_draw_centerpoint', //图层id
        iconcfg: {
          image: '/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/digital-blue.png',
          iconSize: 1,
        },
      })
    },
    mapLine(e) {
      top.mapUtil.removeAllLayers(['circle_draw_centerLine'])
      top.mapUtil.loadPolylineLayer({
        layerid: 'circle_draw_centerLine',
        lines: [
          [Array.isArray(e.center) ? e.center.slice(0, 2) : [e.center.lng, e.center.lat], e.w_cen.slice(0, 2)],
        ], //新地图 接口参数lines
        style: {
          width: 5,
          color: [250, 250, 250, 1],
          // image: baseURL.url + '/static/citybrain/hjbh/img/rkzt/jiantou_Right.png',
        },
      })
    },
    mapText(e) {
      top.mapUtil.removeAllLayers(['circle_draw_centerRadius'])
      top.mapUtil.loadTextLayer({
        layerid: 'circle_draw_centerRadius',
        data: [
          {
            pos: Array.isArray(e.center) ? e.center.slice(0, 2).concat([100]) : [e.center.lng, e.center.lat, 100],
            color: [242, 242, 242, 1],
            text: e.area + 'm',
          },
        ], //数据
        style: {
          size: 30, //文字大小
          color: [242, 242, 242, 1], //文字颜色
        },
      })
    },
    // 点选查询
    layersCheck() {
      this.queryLyrs = []
      let view = top.mapUtil.mapview
      Object.keys(top.mapUtil.layers).forEach((item) => {
        let lyr = top.mapUtil.layers[item]
        //将tcgkwg换成网格_,移除tcgl，移除map
        let newName = item.includes('tcqkwg')
          ? item.replace(new RegExp('tcqkwg', 'g'), '网格_')
          : item.includes('tcgl')
            ? item.replace(new RegExp('tcgl', 'g'), '')
            : item.includes('map')
              ? item.replace(new RegExp('map', 'g'), '')
              : item

        if (lyr.geometryType && lyr.geometryType == 'polygon' && item != 'drawMine') {
          this.nameId[newName] = item
          this.queryLyrs.push({
            value: item,
            label: newName,
            disabled: false,
          })
        }
      })
      this.queryValue != '' && this.queryStart()
    },
    queryStart() {
      let rVm = this
      let clickEvt = window.ArcGisUtils.mapClickEventHandle._callbackEvent
      clickEvt = clickEvt.filter((item) => {
        return item.callback.name != 'queryCallback'
      })
      window.ArcGisUtils.mapClickEventHandle._callbackEvent = clickEvt

      const pointEventId = window.ArcGisUtils.mapClickEventHandle.add(
        top.mapUtil.layers[this.queryValue].id,
        function queryCallback(point, graphic) {
          let befor = [],
            after = []
          if (graphic) {
            graphic.attributes.position = point
            let _geometry_origin = window.parent.ArcgisToGeojsonUtils.arcgisToGeoJSON(graphic.geometry)
            let _geometry_new = { type: 'Polygon', coordinates: [] }
            _geometry_origin.coordinates[0].map((item) => {
              befor.push({
                x: item[0],
                y: item[1],
              })
            })

            //点数大于500时抽稀，其余均计算最小外接矩形
            if (befor.length >= 500) {
              after = simplify(befor, 0.005, true)
            } else {
              rVm.getRect([_geometry_origin.coordinates])[0].map((item) => {
                after.push({
                  x: item[0],
                  y: item[1],
                })
              })
            }
            _geometry_new.coordinates[0] = []
            after.map((item) => {
              _geometry_new.coordinates[0].push([item.x, item.y])
            })
            graphic.attributes.geometry = _geometry_origin
            graphic.attributes.searchgeometry = _geometry_new
            if (_geometry_origin.coordinates.length > 1) {
              graphic.attributes.searchgeometry.coordinates = rVm.getRect(_geometry_origin.coordinates)
            }
          }
          if (graphic.attributes.geometry && rVm.foreClickPolyCode != graphic.attributes.ADCODE) {
            rVm.tableData.map((res) => {
              top.mapUtil.removeLayer('personCount' + res.name)
            })
            rVm.foreClickPolyCode = graphic.attributes.ADCODE
            rVm.createWLGZ({
              type: 'Feature',
              geometry: graphic.attributes.searchgeometry,
              properties: {
                area: Number(graphic.attributes.Shape_Area) || 0,
                searchgeometry: graphic.attributes.searchgeometry,
                panelType: 'clickQuery',
                arclayerid: graphic?.layer.id || '',
              },
            })
            top.mapUtil.removeLayer('video-point') //清除摄像头点位
            rVm.centroid = [graphic.geometry.centroid.x, graphic.geometry.centroid.y]
            rVm.distance = rVm.calculateRadius(
              graphic.geometry.centroid,
              graphic.attributes.searchgeometry.coordinates[0]
            )
            rVm.pointDataFun('zbjk', graphic.attributes.searchgeometry.coordinates) //加载摄像头点位
            window.parent.view.whenLayerView(graphic.layer).then(function (layerView) {
              if (window.clickgra) window.clickgra.remove()
              window.clickgra = layerView.highlight(graphic)
            })
          }
        }
      )
    },
    //创建图形区域id
    createWLGZ(faceMap) {
      let this_ = this
      this.count = '00000'
      this.tableData.map((res) => {
        res.num = ''
        res.avg = ''
        res.arr = []
        top.mapUtil.removeLayer('personCount' + res.name)
      }) //清除poi点位
      // this.$refs.multipleTable.clearSelection()
      if (faceMap.properties.type && faceMap.properties.type.includes('circle')) {
        this_.drawR(faceMap.properties)
      }
      // 创建区域
      if (faceMap.geometry.coordinates.length > 1) {
        //对多要素面及空心面绘制外接矩形，适配查询接口
        let rect = this.getRect(faceMap.geometry.coordinates)
        faceMap.geometry.coordinates = this.getRect(faceMap.geometry.coordinates)
      }
      if (faceMap.geometry.coordinates[0] != []) {
        let str = ''
        faceMap.geometry.coordinates[0].forEach((ele) => {
          str += ele[0] + ',' + ele[1] + '|'
        })
        let faceStr = str.slice(0, str.length - 1)
        axios({
          method: 'post',
          url: `${process.env.VUE_APP_BASE_API_MAP}/typeq/api/getu/project/create`,
          data: {
            shape: faceStr,
            precision: 7,
          },
        }).then(function (res) {
          this_.getCount(res.data, faceMap)
        })
      }
    },
    getCount(id, faceMap) {
      let this_ = this
      let d = new Date()
      let start = `${d.getFullYear()}-${d.getMonth() + 1}-${d.getDate()} ${d
        .getHours()
        .toString()
        .padStart(2, '0')}:${d.getMinutes().toString().padStart(2, '0')}:${d
          .getSeconds()
          .toString()
          .padStart(2, '0')}`
      var frontOneHour = new Date(d.getTime() - 3 * 60 * 60 * 1000)
      let end = `${frontOneHour.getFullYear()}-${frontOneHour.getMonth() + 1
        }-${frontOneHour.getDate()} ${frontOneHour.getHours().toString().padStart(2, '0')}:${frontOneHour
          .getMinutes()
          .toString()
          .padStart(2, '0')}:${frontOneHour.getSeconds().toString().padStart(2, '0')}`

      axios({
        method: 'post',
        url: `${process.env.VUE_APP_BASE_API_MAP}/typeq/api/getu/project/get`,
        data: {
          id: id.data,
          type: 2,
          start_time: end,
          end_time: start,
        },
      }).then(function (res) {
        this_.serchFun({ mapGeoJson: faceMap, countAll: res.data.data })
      })
    },
    async serchFun(data) {
      let _this = this
      console.log('serchFun called with data:', data)
      _this.count = data.countAll.toString()
      console.log('count after update:', _this.count)
      let polygon = data.mapGeoJson.properties.searchgeometry
        ? {
          type: 'Feature',
          geometry: data.mapGeoJson.properties.searchgeometry,
          properties: {},
        }
        : data.mapGeoJson
      let obj = { type: 'FeatureCollection', features: [polygon] }
      for (let index = 0; index < _this.tableData.length; index++) {
        !(function (index) {
          let axiosData = {
            pageInfo: {
              current: 1,
              size: 10000,
              totalSize: 0,
            },
            text: _this.tableData[index].codenum,
            tableNames: 'poi',
            returnGeo: true,
            sortField: '',
            order: '',
            geometry: JSON.stringify(obj),
          }
          axios({
            method: 'post',
            url: `${process.env.VUE_APP_BASE_API_MAP}/api2.0/solr-provider/api/data-sources/solr-search`,
            data: axiosData,
            headers: {
              'Content-Type': 'application/json',
            },
          }).then((res) => {
            top.mapUtil.removeLayer('personCount' + _this.tableData[index].name)
            if (res.data.data == null) {
              _this.tableData[index].arr = []
              _this.tableData[index].num = '-'
              _this.tableData[index].avg = _this.tableData[index].num == 0 ? 0 : '-'
              return
            }
            console.log('res==>', res.data.data.dataList)
            _this.tableData[index].arr = res.data.data
            _this.tableData[index].num =
              res.data.pageInfo.totalSize > res.data.data.length
                ? res.data.pageInfo.totalSize
                : res.data.data.length
            _this.tableData[index].avg =
              _this.tableData[index].num == 0
                ? 0
                : data.countAll == 0
                  ? _this.tableData[index].num
                  : ((_this.tableData[index].num / data.countAll) * 1000).toFixed(1)
          })
        })(index)
      }
    },
    // 计算最小外接圆的半径
    calculateRadius(center, coordinates) {
      let radius = 0 // 圆半径（米）
      // 遍历所有坐标
      coordinates.forEach((item) => {
        // 计算当前坐标与圆心的距离
        const lon = item[0]
        const lat = item[1]
        const distance = this.getDistance(center.y, center.x, lat, lon)
        // 更新圆半径
        if (distance > radius) {
          radius = distance
        }
      })

      return radius / 1000
    },
    //计算多要素面及中空面最小外接矩形
    getRect(coordinates) {
      let minX
      let minY
      let maxX
      let maxY

      if (coordinates[0][0][0][0]) {
        //多要素面
        // 初始化最小外接矩形的坐标为第一个点
        minX = coordinates[0][0][0][0]
        minY = coordinates[0][0][0][1]
        maxX = coordinates[0][0][0][0]
        maxY = coordinates[0][0][0][1]
        // 遍历所有点，更新最小外接矩形的坐标
        for (let i = 0; i < coordinates.length; i++) {
          console.log(coordinates[i])
          for (let j = 0; j < coordinates[i].length; j++) {
            for (let k = 0; k < coordinates[i][j].length; k++) {
              const x = coordinates[i][j][k][0]
              const y = coordinates[i][j][k][1]
              if (x < minX) {
                minX = x
              }
              if (x > maxX) {
                maxX = x
              }
              if (y < minY) {
                minY = y
              }
              if (y > maxY) {
                maxY = y
              }
            }
          }
        }
      } else {
        // 初始化最小外接矩形的坐标为第一个点
        minX = coordinates[0][0][0]
        minY = coordinates[0][0][1]
        maxX = coordinates[0][0][0]
        maxY = coordinates[0][0][1]
        // 遍历所有点，更新最小外接矩形的坐标
        for (let i = 0; i < coordinates.length; i++) {
          for (let j = 0; j < coordinates[i].length; j++) {
            const x = coordinates[i][j][0]
            const y = coordinates[i][j][1]
            if (x < minX) {
              minX = x
            }
            if (x > maxX) {
              maxX = x
            }
            if (y < minY) {
              minY = y
            }
            if (y > maxY) {
              maxY = y
            }
          }
        }
      }

      // 返回最小外接矩形的坐标
      return [
        [
          [minX, minY],
          [maxX, minY],
          [maxX, maxY],
          [minX, maxY],
          [minX, minY],
        ],
      ]
    },
    // 计算两点间距离
    getDistance(lat1, lng1, lat2, lng2) {
      var radLat1 = (lat1 * Math.PI) / 180.0
      var radLat2 = (lat2 * Math.PI) / 180.0
      var a = radLat1 - radLat2
      var b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0
      var s =
        2 *
        Math.asin(
          Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2))
        )
      s = s * 6378.137 // EARTH_RADIUS;
      s = Math.round(s * 10000) / 10000
      return s * 1000
    },
    // 添加权限验证方法
    creditAuth() {
        var appKey = 'zj_jh-API',
            masterSecret = 'vqVINnikQoL8fHbfgLzdzC62'

          var timestamp = Number(Math.round(new Date().getTime() / 1000).toString())

          var time_md5 = CryptoJS.MD5(timestamp.toString()).toString()

          var o_str = appKey + time_md5 + masterSecret,
            sha256_str = CryptoJS.SHA256(o_str).toString()

          var sign = sha256_str + masterSecret

          const reqParams = {
            appKey: appKey,
            sign: sign,
            timestamp: timestamp,
            version: 'v1.0',
          }

          return axios({
            method: 'post',
            url: `${process.env.VUE_APP_BASE_API_MAP}/typeq/api/auth/creditAuth`,
            data: reqParams,
          }).then(function (res) {
            if (res.data.errno === 0) {
              window.accessToken = res.data.data.accessToken

              axios.defaults.headers.common['Access-Token'] = res.data.data.accessToken
            }
          })
    },
    // 点选视频打点
    pointDataFun(label, geojson) {
      let that = this
      axios({
        method: 'get',
        url: `${process.env.VUE_APP_BASE_API_MAP}/jhyjzh-server/screen_api/home/<USER>
        params: {
          type: 'zbjk',
          distance: that.distance, //单位：km
          point: that.centroid.toString(),
        },
      }).then(function (data) {
        let dataArr = []
        const poly = turf.polygon(geojson)
        data.data.data.zbjk.pointData.map((item) => {
          let pot = turf.point([item.lng, item.lat])
          if (turf.booleanPointInPolygon(pot, poly)) {
            let cameraType = item.isHighAltitude == 1 ? '高点' : item.cameraType
            let obj = {
              cameraType: cameraType,
              data: item.data,
              is_online: item.is_online,
              lat: item.lat,
              lng: item.lng,
              pointType: that.getPointType(item.is_online, cameraType),
            }
            dataArr.push(obj)
          }
        })
        that.getManyPoint(dataArr)
      })
    },
    getPointType(is_online, cameraType) {
      let arr = is_online + '-' + cameraType
      let obj = {
        枪机在线: '在线-枪机',
        枪机离线: '离线-枪机',
        球机在线: '在线-球机',
        球机离线: '离线-球机',
        半球机在线: '在线-半球机',
        半球机离线: '离线-半球机',
        高点在线: '在线-高点',
        高点离线: '离线-高点',
        未知在线: '在线-未知',
        未知离线: '离线-未知',
      }
      for (let key in obj) {
        if (obj[key] == arr) {
          return key
        }
      }
    },
    //一次绘制多种不同类型的点
    getManyPoint(pointData, pointId) {
      console.log('r', pointData)
      top.mapUtil.loadPointLayer({
        layerid: 'video-point',
        data: pointData,
        onclick: this.openPointMassage,
        onblur: this.onblur,
        cluster: true, //是否定义为聚合点位：true/false
        iconcfg: {
          image: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiangji.png`,
          iconSize: 0.5,
          iconlist: {
            field: 'pointType',
            list: [
              {
                value: '枪机在线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiangji.png`,
              },
              {
                value: '枪机离线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-qiangji.png`,
              },
              {
                value: '球机在线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiuji.png`,
              },
              {
                value: '球机离线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-qiuji.png`,
              },
              {
                value: '半球机在线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-banqiu.png`,
              },
              {
                value: '半球机离线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-banqiu.png`,
              },
              {
                value: '高点在线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-gaodian.png`,
              },
              {
                value: '高点离线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-gaodian.png`,
              },
              {
                value: '未知在线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-gaodian.png`,
              },
              {
                value: '未知离线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-gaodian.png`,
              },
            ],
          },
        },
      })
    },
    // 查看地图点位点击的详情
    openPointMassage(e, list) {
      // 这里可以添加点击事件的处理逻辑
      console.log('点击事件', e, list)
    },
    //视屏移入事件
    onblur(e) {
      // 这里可以添加鼠标移入事件的处理逻辑
      console.log('鼠标移入事件', e)
    },
    handleCheckboxChange(event, row) {
      const checked = event.target.checked;
      if (checked) {
        this.clickName.push(row);
        // let mainObj = this.isHas(this.clickName, row) //赛选减的目标
        let pointData = []
        row.arr.map((item) => {
          let str = {
            lng: item.x,
            lat: item.y,
            name: item.name,
            address: item.address || item.city + item.county + item.town,
          }
          if (item.x != '' && item.x != null && item.x != undefined) {
            pointData.push(str)
          }
        })
        let pointId = 'personCount' + row.name
        let iconUrl = `/map/img/${row.icon}.png`
        this.pointTextMapFun(iconUrl, pointData, pointId, 1)
      } else {
        const index = this.clickName.findIndex(item => item.name === row.name);
        if (index > -1) {
          this.clickName.splice(index, 1);
        }
        top.mapUtil.removeLayer('personCount' + row.name)
      }
      this.handleSelectionChange(this.clickName);
    },
  }
}
</script>

<style scoped>
.contentmain {
  position: relative;
  color: #fff;
  font-size: 30px;
  background-size: 100% 100%;
  padding: 40px 20px 20px 20px;
  box-sizing: border-box;
  height: 100%;
  overflow: auto;
}

.header {
  height: 50px;
  line-height: 50px;
  font-size: 30px;
  font-weight: bolder;
  padding-left: 25px;
  background: url('@/assets/map/header.svg') no-repeat;
  background-size: 100% 100%;
}

.items-title {
  height: 30px;
  margin-bottom: 10px;
}

.items-title>div {
  float: right;
  padding: 0 16px;
  height: 35px;
  cursor: pointer;
  border: 1px solid hsla(0, 0%, 100%, 0.30980392156862746);
  box-sizing: border-box;
  transition: 0.3s;
  line-height: 30px;
  font-size: 25px;
  background: linear-gradient(180deg, hsla(0, 0%, 100%, 0.4), hsla(0, 0%, 100%, 0));
  color: white;
}

.items-title>div:hover,
#active {
  background: #2960cb;
  color: white;
}

/* 表格样式修改 */
.hearder_h2 {
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  font-size: 30px;
  font-weight: 500;
  text-shadow: 0px 4px 9px rgba(0, 0, 0, 0.29);
  background: linear-gradient(180deg, #caffff 0%, #caffff 0%, #ffffff 0%, #00c0ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.hearder_h2>span {
  background: linear-gradient(180deg, #aed6ff 0%, #74b8ff 47.4853515625%, #9ccfff 50%, #ddeeff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0 10px;
  white-space: nowrap;
}

:deep(.quyuchaxun .el-table) {
  max-height: 420px !important;
  overflow: hidden;
  overflow-y: auto;
  color: rgb(197, 192, 192);
  padding-right: 5px !important;
  background: transparent !important;
}

:deep(.quyuchaxun .el-table th),
:deep(.quyuchaxun .el-table tr) {
  font-size: 22px !important;
}

:deep(.quyuchaxun .el-table tr) {
  background: url('@/assets/map/table_tr_bg.png') no-repeat;
  background-size: 99% 97%;
}

:deep(.quyuchaxun .el-table td),
:deep(.quyuchaxun .el-table th.is-leaf) {
  border: 0 !important;
}

:deep(.quyuchaxun .el-table tbody tr:hover>td) {
  background: #1d4a7acb !important;
}

:deep(.quyuchaxun .el-table::-webkit-scrollbar) {
  /*滚动条整体样式*/
  width: 2px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}

:deep(.quyuchaxun .el-table::-webkit-scrollbar-thumb) {
  border-radius: 2px;
  background: #20aeff;
  height: 10px;
}

:deep(.quyuchaxun .el-table--border::after),
:deep(.quyuchaxun .el-table--group::after),
:deep(.quyuchaxun .el-table::before) {
  background-color: transparent;
}

:deep(.quyuchaxun .el-checkbox),
:deep(.quyuchaxun .el-checkbox__input) {
  zoom: 120%;
}

:deep(.quyuchaxun .el-table .cell) {
  line-height: normal;
}

:deep(.quyuchaxun .el-table .el-table__cell) {
  padding: 8px 0 15px !important;
}

:deep(.quyuchaxun .el-checkbox__inner) {
  width: 15px;
  height: 15px;
}

.number {
  display: inline-block;
  font-size: 35px;
  margin: 0 2px;
}

.number .numbg {
  display: inline-block;
  width: 30px;
  height: 41px;
  line-height: 41px;
  text-align: center;
  background: url('@/assets/map/numBg.png') no-repeat;
  background-size: contain;
  margin: 0 2px;
  border-radius: 8px;
}

/* el */
:deep(.quyuchaxun .el-input) {
  font-size: 30px;
}

:deep(.quyuchaxun .el-input__inner) {
  height: 40px;
  line-height: 30px;
  background-color: rgba(25, 27, 35, 0);
  color: hsla(0, 0%, 100%, 0.8);
}

:deep(.quyuchaxun .el-input__icon) {
  height: 30px;
  line-height: 30px;
}

:deep(.quyuchaxun .popper__arrow) {
  display: none !important;
}

:deep(.quyuchaxun .el-select) {
  margin: 0 20px;
}

:deep(.quyuchaxun .el-select-dropdown) {
  background-color: rgba(25, 27, 35, 0.96);
}

:deep(.quyuchaxun .el-popper[x-placement^='bottom']) {
  margin-top: -2px;
}

:deep(.quyuchaxun .el-select-dropdown__item) {
  color: #fff;
  height: 40px;
  line-height: 40px;
  font-size: 30px;
}

:deep(.quyuchaxun .el-select-dropdown__item.hover),
:deep(.quyuchaxun .el-select-dropdown__item:hover) {
  background-color: #2a4b8b;
}

:deep(.quyuchaxun .el-select-dropdown__item.selected) {
  background-color: #2a4b8b;
  color: #fff;
  font-weight: 400;
}

#circle_sou>div {
  font-size: 30px;
  padding: 2px 7px 3px 4px;
  border-radius: 14px;
  cursor: pointer;
}

.icon-nav-start {
  background: linear-gradient(45deg, #3a3636, #0001b9);
}

.icon-nav-start:hover,
.icon-nav-start-active {
  background: linear-gradient(45deg, #2f2c2c, #3579f8);
}

:deep(.quyuchaxun .el-slider__runway) {
  width: 400px;
  height: 10px;
  border-radius: 10px;
  margin-left: 20px;
}

:deep(.quyuchaxun .el-slider__bar) {
  height: 10px;
}

:deep(.quyuchaxun .el-slider__stop) {
  height: 10px;
  width: 10px;
  background-color: #294e7ad4;
}

:deep(.quyuchaxun .el-slider__button) {
  width: 7.7px;
  height: 7.7px;
  margin-top: -8px;
  margin-left: 10px;
  background-color: #0083ff;
  border-color: #fff;
  border-radius: 0;
  border-width: 1px;
  transform: translateX(-50%) rotate(45deg) !important;
}

:deep(.quyuchaxun .el-slider__button:after) {
  content: '';
  position: absolute;
  background-image: url('@/assets/map/slider.svg');
  width: 30px;
  height: 30px;
  left: -11px;
  top: -11px;
  transform: rotate(45deg);
  background-size: 100%;
}

:deep(.quyuchaxun .el-slider__runway.show-input) {
  margin-right: 145px;
}

:deep(.quyuchaxun .el-input-number--small .el-input__inner) {
  padding-left: 20px;
  padding-right: 20px;
}

:deep(.quyuchaxun .el-input-number--small .el-input-number__decrease),
:deep(.el-input-number--small .el-input-number__increase) {
  width: 25px;
}

:deep(.quyuchaxun .el-input-number__decrease),
:deep(.quyuchaxun .el-input-number__increase) {
  background: transparent;
  color: #fff;
  font-weight: bold;
}

:deep([class*=' el-icon-']),
:deep([class^='el-icon-']) {
  font-weight: 700;
}

/* 通用样式类 */
.s-flex {
  display: flex;
  align-items: center;
}

.s-row-center {
  justify-content: center;
}

.s-font-26 {
  font-size: 26px;
}

.s-c-white {
  color: white;
}

.s-text-center {
  text-align: center;
}

.s-m-10 {
  margin: 10px;
}

.s-m-b-10 {
  margin-bottom: 10px;
}

.s-c-yellow-gradient {
  background: linear-gradient(180deg, #ffeb3b 0%, #ff9800 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.s-c-blue-gradient1 {
  background: linear-gradient(180deg, #2196f3 0%, #03a9f4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 自定义表格样式 */
.custom-table {
  width: 100%;
  margin-top: 20px;
  color: rgb(197, 192, 192);
  font-size: 22px;
}

.table-container {
  max-height: 500px;
  overflow-y: auto;
  padding-right: 5px;
}

.table-row {
  display: flex;
  align-items: center;
  background: url('@/assets/map/table_tr_bg.png') no-repeat;
  background-size: 99% 97%;
  margin-bottom: 2px;
  padding: 8px 0;
}

.table-row:hover {
  background: #1d4a7acb !important;
}

.table-cell {
  flex: 1;
  padding: 0 10px;
  display: flex;
  align-items: center;
}

.name-cell {
  flex: 2;
}

.checkbox-cell {
  flex: 0 0 50px;
  justify-content: center;
}

.custom-checkbox {
  width: 20px;
  height: 20px;
  cursor: pointer;
  position: relative;
  appearance: none;
  -webkit-appearance: none;
  border: 1px solid #20aeff;
  border-radius: 2px;
  background: transparent;
}

.custom-checkbox:checked {
  background: #20aeff;
}

.custom-checkbox:checked::after {
  content: '';
  position: absolute;
  left: 4px;
  top: 1px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* 自定义滚动条 */
.table-container::-webkit-scrollbar {
  width: 2px;
  height: 1px;
}

.table-container::-webkit-scrollbar-thumb {
  border-radius: 2px;
  background: #20aeff;
  height: 10px;
}

.table-container::-webkit-scrollbar-track {
  background: transparent;
}
</style>