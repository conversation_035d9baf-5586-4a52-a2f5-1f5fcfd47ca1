<template>
  <div class="panelWrap">
    <div class="titleClass">视频设备</div>
    <div class="input-with-select">
      <el-input placeholder="搜索" v-model="searchValue">
        <el-button slot="append" icon="el-icon-search" @click="init"></el-button>
      </el-input>
    </div>
    <div class="treeWrap">
      <div class="treeItem" v-for="item in list" :key="item.value">
        <div class="parentLabel">
          <div class="labeleft">
            <img :class="{'arrowClass':!(showValue == item.value),'arrowLeftClass':showValue == item.value,'noSelect':true}" :src="require(`@/assets/common/xarrow.png`)" @click="showChild(item)">
            <div class="noSelect">{{item.label}}</div>
          </div>
          <img v-show="!item.statu" class="wkuangClass noSelect" :src="require(`@/assets/common/wkuang.png`)" @click="selectCheckBox(item,1)">
          <img v-show="item.statu" class="wkuangClass noSelect" :src="require(`@/assets/common/xkuang.png`)" @click="selectCheckBox(item,1)">
        </div>
        <transition name="mybox">
          <div v-show="showValue == item.value">
            <div class="parentLabel childLabel" v-for="(it,i) in item.children" :key="i">
              <div class="labeleft">
                <img class="spIconClass noSelect" :src="require(`@/assets/common/spIcon.png`)">
                <div class="itxt" :title="it.label">{{it.label}}</div>
              </div>
              <img v-show="!it.statu" class="wkuangClass noSelect" :src="require(`@/assets/common/wkuang.png`)" @click="selectCheckBox(it,2,item)">
              <img v-show="it.statu" class="wkuangClass noSelect" :src="require(`@/assets/common/xkuang.png`)" @click="selectCheckBox(it,2,item)">
            </div>
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex'
import {getVideoList} from "@/api/video";
export default {
  data () {
    return {
      searchValue: '',
      list: [],
      showValue: null,
      checkedList: []
    }
  },
  computed: {
    ...mapGetters(['spsbCheckList'])
  },
  methods: {
    ...mapMutations('mapStatus', ['SPSB_HISTORY']),
    init() {
      // let AllPointLayer = Object.keys(mapUtil.layers).slice(1,Object.keys(mapUtil.layers).length);
      // AllPointLayer.forEach((item,i) => {
      //   this.rmAllLayer("", {value: item});
      // })
      this.cleanVideoPointLayers()
      getVideoList({orgname:this.searchValue}).then(res => {
        this.list = res.data.data;
        this.list.forEach((item,i) => {
          item.value = "video" + i;
          if (item.children) {
            item.children.forEach((obj,j) => {
              obj.value = item.value
            })
          }
        })
      })
    },
    //清除原有的所有图层
    cleanVideoPointLayers() {
      // 确保 top.mapUtil 和 top.mapUtil.layers 是可用的
      if (!top || !top.mapUtil || typeof top.mapUtil.layers !== 'object') {
        console.error('mapUtil or mapUtil.layers is not available');
        return;
      }

      // 使用常量定义正则表达式，以提高代码的可维护性
      const mapWarningLayerRegex = /video/;

      // 获取所有层的键，并筛选出匹配 MapWarningList 的层
      const layerKeys = Object.keys(top.mapUtil.layers).filter(item => mapWarningLayerRegex.test(item));

      // 检查是否有匹配的层
      if (layerKeys.length === 0) {
        console.log('No warning layers found to clean');
        return;
      }

      // 安全地移除匹配的层
      try {
        top.mapUtil.removeAllLayers(layerKeys);
      } catch (error) {
        console.error('Failed to remove warning layers:', error);
      }
    },
    showChild (item) {
      this.showValue == item.value ? this.showValue = null : this.showValue = item.value
    },
    selectCheckBox (row, node, parentRow) {
      // console.log('勾选的时候做了什么事', row, node, parentRow)
      if (!row.statu) {                             // 勾选
        switch (node) {
          case 1:                                   // 选择父节点
            this.$set(row, 'statu', 1)
            row.children.forEach(ele => {
              this.$set(ele, 'statu', 1)
            })
            this.checkedList = row.children;
            break
          case 2:                                   // 选择子节点
            this.$set(row, 'statu', 1)
            let flagArray = parentRow.children.filter(el => {  // 判断子节点是否已全部勾选
              return el.statu == 0
            })
            if (flagArray.length == 0) {
              this.$set(parentRow, 'statu', 1)
            }

            //找出勾选项的对应父级目录下的所有勾选项并重新上图
            let matchItem = this.list.find(item => row.value.indexOf(item.value) != -1).children;
            let checkedList = matchItem.filter(item => item.statu == 1);
            this.checkedList = checkedList
            break
        }
      } else {
        switch (node) {                             // 取消勾选
          case 1:                                   // 取消选择父节点
            this.$set(row, 'statu', 0)
            row.children.forEach(ele => {
              this.$set(ele, 'statu', 0)
            })
            this.checkedList = [];
            break
          case 2:                                   // 取消选择子节点
            this.$set(row, 'statu', 0)
            this.$set(parentRow, 'statu', 0)        // 取消父节点勾选
            //找出取消勾选项的对应父级目录下的所有勾选项并重新上图
            let matchItem = this.list.find(item => row.value.indexOf(item.value) != -1).children;
            let checkedList = matchItem.filter(item => item.statu == 1);
            console.log(checkedList,"checkList");
            this.checkedList = checkedList
            break
        }
      }
      this.rmAllLayer(node, row)
      if (this.checkedList.length > 0) {
        this.addMarker('a', 1, {value:row.value,children: this.checkedList})
      }
    },
    addMarker (type, node, key) {
      this.$EventBus.$emit('drawPoint', type, node, key)
    },
    rmAllLayer (node, key) {
      this.$EventBus.$emit('rmAllLayer', node, key)
    }
  },
  mounted () {
    // console.log('打印历史list', this.spsbCheckList)
    if (this.spsbCheckList && this.spsbCheckList.length > 0) {
      this.list = this.spsbCheckList
    }
    this.init()
  },
  beforeDestroy () {
    this.SPSB_HISTORY(this.list)
  }
}
</script>

<style lang="less" scoped>
.panelWrap {
  width: 450px;
  height: 1227px;
  background: linear-gradient(180deg, rgba(14, 26, 64, 0.85), rgba(6, 64, 105, 0.85));
  margin-left: 16px;
  display: flex;
  flex-direction: column;

  .titleClass {
    width: 100%;
    height: 64px;
    background: linear-gradient(0deg, #073446, #00aae2);
    font-size: 32px;
    font-family: Source Han Sans CN;
    font-weight: bold;
    color: #ffffff;
    box-sizing: border-box;
    padding: 15px 0 0 45px;
    margin-bottom: 16px;
  }
  .input-with-select {
    padding: 0 14px 0 16px;
    /deep/ .el-input-group {
    }

    /deep/ .el-input__inner {
      background-color: #354c66;
      height: 52px;
      border: 2px solid #cde7fe;
      border-right: none;
      padding-left: 30px;
      font-size: 32px;
      color: #c0d6ed;
    }

    /deep/ .el-input__inner::placeholder {
      font-size: 32px;
      font-family: Source Han Sans SC;
      font-weight: 400;
      color: #c0d6ed;
      padding-top: 10px;
    }

    /deep/ .el-input-group__append {
      padding: 0;
      background-color: #354c66;
      border: 2px solid #cde7fe;
      font-size: 32px;
      color: #c0d6ed;
      padding-right: 20px;
      border-left: none;
    }
  }
  .treeWrap {
    width: 100%;
    flex: 1;
    margin-top: 9px;
    box-sizing: border-box;
    padding: 0 13px 0 8px;
    overflow: hidden;
    overflow-y: auto;
    // background-color: antiquewhite;
    // opacity: 0.5;
    .treeItem {
      .parentLabel {
        margin-top: 12px;
        width: 100%;
        height: 60px;
        background: linear-gradient(94deg, rgba(3, 97, 156, 0) 0%, #03619c 100%);
        border-radius: 0px 30px 30px 0px;
        box-sizing: border-box;
        padding: 0 20px 0 38px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .labeleft {
          display: flex;
          align-items: center;
          font-size: 32px;
          font-family: Source Han Sans SC;
          font-weight: 400;
          color: #ffffff;
          .arrowClass {
            width: 24px;
            height: 16px;
            margin-right: 28px;
            cursor: pointer;
            transition: 0.2s;
            transform-origin: center;
            transform: rotateZ(-90deg);
          }
          .arrowLeftClass {
            width: 24px;
            height: 16px;
            margin-right: 28px;
            cursor: pointer;
            transition: 0.2s;
            transform-origin: center;
            transform: rotateZ(0deg);
          }
          .spIconClass {
            width: 34px;
            height: 26px;
            margin-right: 25px;
          }
        }
        .wkuangClass {
          cursor: pointer;
        }
      }
      .childLabel {
        background: rgba(14, 26, 64, 0);
        transition: 0.3s;
        // cursor: pointer;
        &:hover {
          background: linear-gradient(94deg, rgba(3, 97, 156, 0) 0%, #03619c 100%);
          border-radius: 0px 30px 30px 0px;
        }
        .itxt {
          width: 285px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 32px;
          font-family: Source Han Sans SC;
          font-weight: 400;
          color: #c0d6ed;
          user-select: none;
        }
      }
    }
  }
}

.noSelect {
  user-select: none;
}

.mybox-leave-active,
.mybox-enter {
  height: 0px;
  opacity: 0;
  transition: 0.3s;
}
.mybox-leave,
.mybox-enter-active {
  height: auto;
  opacity: 1;
  // transition: opacity .3s
  transition: 0.3s;
}
</style>