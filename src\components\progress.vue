<!--圆环进度图-->
<template>
  <div>
    <div class="PieCharts" :id="'PieCharts' + id" :style="{width: width + 'px',height: height + 'px'}"></div>
  </div>
</template>

<script>
export default {
  props: {
    //数值
    num: {
      type: Number,
      default: 0
    },
    //文字颜色
    textColor: {
      type:String,
      default:"#0580f2"
    },
    //文字大小
    fontSize: {
      type:String,
      default:"16"
    },
    //圆环颜色
    lineColor: {
      type:Array,
      default:() => ['#00cefc','#367bec']
    },
    //圆环槽背景色
    lineBackground: {
      type:String,
      default:'rgba(176, 212, 251, 1)'
    },
    id: {
      type:Number,
      default:0
    },
    width: {
      type:String,
      default:"60"
    },
    height: {
      type:String,
      default:"60"
    },
    isPercent: {
      type:Boolean,
      default:true
    }
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {
    this.initCharts1()
  },
  methods: {
    initCharts1() {
      const that = this
      let myChart = this.$echarts.init(document.getElementById("PieCharts"+ this.id));
      let option = {
        title: {
          text: that.num + (that.isPercent?'%':''),
          x: 'center',
          y: 'center',
          textStyle: {
            fontWeight: 800,
            color: that.textColor,
            fontSize: that.fontSize,
            fontFamily:'YouSheBiaoTiHei-Bold, YouSheBiaoTiHei'
          }
        },
        color: that.lineBackground,
        legend: {
          show: false,
          itemGap: 12,
          data: ['01', '02']
        },

        series: [{
          name: 'Line 1',
          type: 'pie',
          clockWise: true,
          radius: ['90%', '100%'],
          itemStyle: {
            normal: {
              label: {
                show: false
              },
              labelLine: {
                show: false
              }
            }
          },
          hoverAnimation: false,
          data: [{
            value: that.num,
            name: '01',
            itemStyle: {
              normal: {
                color: { // 完成的圆环的颜色
                  colorStops: [{
                    offset: 0,
                    color: that.lineColor[0] // 0% 处的颜色
                  }, {
                    offset: 1,
                    color: that.lineColor[1] // 100% 处的颜色
                  }]
                },
                label: {
                  show: false
                },
                labelLine: {
                  show: false
                }
              }
            }
          }, {
            name: '02',
            value: 100 - that.num
          }]
        }]
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', param => {
        myChart.getZr().setCursorStyle('default')
      })
    },
  },
  watch: {}
}
</script>

<style scoped>

</style>