import Vue from 'vue';
import Router from 'vue-router';
import { options } from './config';
const originalPush = Router.prototype.push;
Router.prototype.push = function push(location, resolve, reject) {
    if ( resolve || reject ) return originalPush.call(this, location, resolve, reject)
    return originalPush.call(this, location).catch(err => err)
}

Vue.use(Router);

function initRouter() {
    return new Router(options);
}

export {
    initRouter
}