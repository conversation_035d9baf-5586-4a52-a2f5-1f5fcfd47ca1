<template>
  <div class="funcListContainer">
    <div class="funcListContainer-item arrow-top" v-show="show == true" @click="openClose"></div>
    <div class="funcListContainer-item arrow-bottom" v-show="show == false" @click="openClose"></div>
    <div class="list" v-show="show == true">
      <div class="funcListContainer-item"
           @click="funClick(item)"
           :title="item.name"
           v-for="(item,i) in funcList"
           :key="i"
           :class="{ 'active': item.selected }"
           :style="{backgroundImage:  `url(${item.img})`}"
      />
    </div>
  </div>
</template>

<script>
import { getCsdnInterface, getCsdnInterface2 } from '@/api/csdnIndexApi'
export default {
  name: "MapFuncList",
  props: {
    // 当前选中的功能名称
    currentFunc: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      show:true,
      qiehuanMap: false,
      qyzfKG: false,
      funcList: [
        {
          img:require("@/assets/map/地图切换b.png"),
          name:"地图切换",
          selected: false
        },
        {
          img:require("@/assets/map/测量工具b.png"),
          name:"测量工具",
          selected: false
        },
        {
          img:require("@/assets/map/空间分析b.png"),
          name:"空间分析",
          selected: false
        },
        {
          img:require("@/assets/map/坐标转换.png"),
          name:"坐标转换",
          selected: false
        },
        {
          img:require("@/assets/map/绘图工具b.png"),
          name:"标绘",
          selected: false
        },
        {
          img:require("@/assets/map/拨号.png"),
          name:"拨打电话",
          selected: false
        },
        {
          img:require("@/assets/map/路径规划.png"),
          name:"路径规划",
          selected: false
        },
        {
          img:require("@/assets/map/区域查询b.png"),
          name:"区域查询",
          selected: false
        },
        {
          type:"page",
          img:require("@/assets/map/城市体征.png"),
          name:"城市体征",
          route:"/UrbanSigns",
          selected: false
        },
        {
          type:"page",
          img:require("@/assets/map/企业智服.png"),
          name:"企业智服",
          route:"/qyzf",
          selected: false
        }
      ]
    }
  },
  computed: {
    // 根据外部传入的currentFunc，计算每个功能的选中状态
    computedFuncList() {
      return this.funcList.map(item => ({
        ...item,
        selected: item.name === this.currentFunc
      }));
    }
  },
  mounted() {
  },
  methods: {
    funClick(item) {
      // 只处理企业智服的选中效果
      if (item.name === "企业智服") {
        // 切换企业智服的选中状态
        this.qyzfKG = !this.qyzfKG;
        
        // 根据 qyzfKG 状态设置选中效果
        this.funcList.forEach(func => {
          if (func.name === "企业智服") {
            func.selected = this.qyzfKG;
          }
        });
        
        this.loadQyzfPoints();
        
        if (this.qyzfKG) {
          this.$emit("openPage", item.route);
        }
      } else {
        // 其他功能不处理选中效果，只执行其功能
        if (item.name == "地图切换") {
          this.qiehuanMap = !this.qiehuanMap;
          if (!this.qiehuanMap) {
            top.ArcGisUtils.exchangeMap(top.mapUtil.mapview, 'image2023');
          } else {
            top.ArcGisUtils.exchangeMap(top.mapUtil.mapview, {
              title: 'BLUEGrey0830',
              layerConfigs: {
                id: 'ebdd252e943146439309da101a87a1f7',
                type: 'tile',
                url: 'https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/ditu0830/MapServer',
              },
            });
          }
        } else if (item.name == "拨打电话") {
          this.$emit("showNumberKeyboard");
        } else if (item.type && item.type == "page") {
          this.$emit("openPage", item.route);
        } else {
          this.$emit("funClick", item.name);
        }
      }
    },
    openClose() {
      this.show = !this.show
      if (this.show == false) {
        this.$emit("closeFunDialog")
      }
    },
    loadQyzfPoints() {
      if (this.qyzfKG) {
        getCsdnInterface('/qyhx_kfq_gsqyjwd').then((res) => {
          let markerList = res.data.data.map(obj => ({
            code: obj.tyshxydm,
            name: obj.qymc,
            lng: obj.jd,
            lat: obj.wd,
            type: 'qyzf'
          }))
          this.$EventBus.$emit('qyzfPoints', markerList, 'qyzfPoints', '/map/img/eventTaskCenter/cszl.png', false, true, false)
        })
      } else {
        top.mapUtil.removeLayer('qyzfPoints')
      }
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .funcListContainer {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: column;
    .funcListContainer-item {
      width: 50px;
      height: 50px;
      background-size: cover;
      margin-top: 10px;
      cursor: pointer;
      position: relative;
      transition: all 0.3s ease;
    }
    .arrow-top {
      background: url("~@/assets/map/收起.png") no-repeat;
    }
    .arrow-bottom {
      background: url("~@/assets/map/展开.png") no-repeat;
    }
    .list {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-direction: column;
    }
    &-item.active {
      border: 2px solid #98ec0f;
      box-shadow: 0 0 10px #98ec0f;
      
      &::after {
        content: "";
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 10px;
        height: 10px;
        background-color: #98ec0f;
        border-radius: 50%;
      }
    }
  }
</style>