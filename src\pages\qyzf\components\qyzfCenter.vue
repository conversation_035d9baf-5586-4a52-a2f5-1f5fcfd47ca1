<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-04-25 09:50:18
 * @LastEditors: wjb
 * @LastEditTime: 2025-09-18 11:12:52
-->
<template>
  <div class="container_center">
    <!-- 顶部打开企业弹窗的搜索框 -->
    <!-- <div class="search-con">
      <div class="qyinfo-search-input" @click.stop="openDialog">
        <el-input placeholder="找企业" class="input-with-select" :readonly="true"></el-input>
      </div>
      <div class="search-btn el-icon-search"></div>
    </div> -->
  </div>
</template>

<script>
export default {
  name: 'qyzfCenter',
  data() {
    return {}
  },
  components: {},
  computed: {},
  mounted() {},
  methods: {
    openDialog() {
      this.$emit('openDialog')
    },
  },
  watch: {},
}
</script>

<style scoped lang="less">
.container_center {
  width: 2960px;
  height: 1606px;
  background: url('@/pages/qyzf/img/qyzf_center.png') 0 0 no-repeat;
  background-size: cover;
  margin: 100px 200px 0;

  .search-con {
    width: 2500px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    margin: 0 auto;
    position: absolute;
    top: 0px;
    left: calc(50% - 1250px);
    z-index: 999;
    margin-bottom: 40px;

    .qyinfo-search-input {
      width: 100%;
      height: 85px !important;
      line-height: 85px;
      color: #fff;
      font-size: 36px;
      position: relative;
    }

    .search-btn {
      width: 50px;
      height: 50px;
      font-size: 42px;
      color: #7f97b2;
      text-align: center;
      line-height: 85px;
      font-weight: bold;
      cursor: pointer;
      position: absolute;
      right: 30px;
    }

    ::v-deep .el-input__inner {
      height: 85px !important;
      line-height: 85px;
      background-color: #29528694;
      font-size: 36px;
      width: 100%;
      color: #fff;
      border: 1px solid #3e618f94 !important;
      border-radius: 40px !important;
      padding: 0 80px 0 50px;
      cursor: pointer !important;
    }
  }
}
</style>
