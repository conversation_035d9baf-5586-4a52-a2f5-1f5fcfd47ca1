<template>
  <div class="weather_box">
    <div class="close">
      <i class="el-icon-close" @click="close" style="cursor: pointer;font-size: 30px;color: #ffffff;"></i>
    </div>
    <div class="box" style="margin-top: 0px">
      <el-switch v-model="timeSwitch" inactive-text="时间与现实同步" active-color="#409EFF" inactive-color="#484A47"
                 @change="lightNowOr"></el-switch>
      <ul class="weather_ul">
        <li v-for="(item,index) in timeList" :class="[timeIndex==index?'bg':'']"
            @click="timeIndex = timeIndex== index? null : index;lightFun(item,timeIndex==null?1:index);">
          <img :src="item.img" alt="" />
          <span>{{item.name}}</span>
        </li>
      </ul>
    </div>
    <div class="split"></div>
    <div class="box">
      <el-switch v-model="weatherSwitch" inactive-text="天气与现实同步" active-color="#409EFF" inactive-color="#484A47"
                 @change="weatherNowOr"></el-switch>
      <ul class="weather_ul">
        <li v-for="(item,index) in weatherList" :class="[weatherIndex==index?'bg':'']"
            @click="weatherIndex = weatherIndex== index? null : index;weatherFun(item,weatherIndex==null?0:index);">
          <img :src="item.img" alt="" />
          <span>{{item.name}}</span>
        </li>
      </ul>
    </div>
<!--    <div class="split"></div>-->
<!--    <div class="box">-->
<!--      <el-switch v-model="autoSwitch" inactive-text="自动导览模式" active-color="#409EFF" inactive-color="#484A47">-->
<!--      </el-switch>-->
<!--    </div>-->
  </div>
</template>

<script>
import {MapInitTimer} from "@/utils";
export default {
  name: "index",
  data() {
    return {
      timeSwitch:true,
      timeList: [
        {
          img: require('@/assets/map/weather/日出.png'),
          name: '早晨',
        },
        {
          img: require('@/assets/map/weather/太阳.png'),
          name: '中午',
        },
        {
          img: require('@/assets/map/weather/日落.png'),
          name: '傍晚',
        },
        {
          img: require('@/assets/map/weather/icon_晚上.png'),
          name: '夜晚',
        },
      ],
      timeIndex: null,

      weatherSwitch: true,
      weatherList: [
        {
          img: require('@/assets/map/weather/太阳.png'),
          name: '晴朗',
        },
        {
          img: require('@/assets/map/weather/多云.png'),
          name: '多云',
        },
        {
          img: require('@/assets/map/weather/雾.png'),
          name: '雾',
        },
        {
          img: require('@/assets/map/weather/下雨-中雨.png'),
          name: '下雨',
        },
        {
          img: require('@/assets/map/weather/小雪.png'),
          name: '下雪',
        },
      ],
      weatherIndex: null,

      autoSwitch: false,
      timer: null
    }
  },
  computed: {},
  mounted() {
    //保证地图初始化完成后调用
    this.timer = MapInitTimer(this.lightNowOr.bind(this), this.weatherNowOr.bind(this));
  },
  methods: {
    close() {
      this.$emit('close')
    },
    lightNowOr() {
      this.timeIndex = null //使当前光照与选定光照互斥
      if (this.timeSwitch && top.window.mapUtil.mapview) {
        top.window.mapUtil.mapview.environment.lighting.date = new Date()
      } else {
        this.lightFun('', 1)
      }
    },
    lightFun(item, index) {
      this.timeSwitch = false //使当前光照与选定光照互斥
      let view = top.window.mapUtil.mapview
      let lightSwitch = {
        0: 'Wed Mar 15 2023 8:01:22 GMT+0800 (中国标准时间)',
        1: 'Wed Mar 15 2023 12:01:22 GMT+0800 (中国标准时间)',
        2: 'Wed Mar 15 2023 18:01:22 GMT+0800 (中国标准时间)',
        3: 'Wed Mar 15 2023 00:01:22 GMT+0800 (中国标准时间)',
      }
      view.environment.lighting.date = new Date(lightSwitch[index])
    },
    weatherNowOr() {
      this.weatherIndex = null //使当前天气与选定天气互斥
      if (this.weatherSwitch) {
        top.window.mapUtil._weatherSwitch = true
        top.window.mapUtil.loadWeatherEffect({ city: "开发区" })
      } else {
        this.weatherFun('', 0)
      }
    },
    weatherFun(item, index) {
      this.weatherSwitch = false //使当前天气与选定天气互斥
      top.window.mapUtil._weatherSwitch = false
      let view = top.window.mapUtil.mapview
      let weatherType = {
        0: { type: 'sunny', cloudCover: 0.5 },
        1: { type: 'cloudy', cloudCover: 0.8 },
        2: { type: "foggy", fogStrength: 0.1, },
        3: { type: 'rainy', precipitation: 0.5, cloudCover: 0.4 },
        4: { type: 'snowy', precipitation: 0.6, cloudCover: 0.3 },
      }
      view.environment.weather = weatherType[index]
    },
  },
  watch: {},
  beforeDestroy() {
    // 在组件销毁前清除定时器
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
}
</script>

<style scoped>
* {
  padding: 0;
  margin: 0;
}

.close {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

.bg {
  background-color: #172468;
}

.split {
  width: 380px;
  height: 2px;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.98) 46%, rgba(255, 255, 255, 0) 100%);
}

.weather_box {
  width: 437px;
  height: 452px;
  background-image: url("~@/assets/map/weather/weather-bg.png");
  padding: 0 30px;
  padding-top: 48px;
  box-sizing: border-box;
  position: absolute;
  z-index: 999;
  left: 7200px;
  top: 200px;
  background-size: 100% 100%;
}

.select_box {
  width: 100%;
  height: 60px;
  position: relative;
}

.select {
  width: 219px;
  height: 46px;
  left: 0;
}

.ul>div,
.ul ul>li {
  height: 45px;
  line-height: 45px;
}

.ul {
  height: 45px;
  font-size: 24px;
  border-color: transparent;
  border-width: 0;
  /* background-color: rgba(0, 0, 0, 0.075);
  background-image: linear-gradient(to bottom right, rgba(205, 205, 205, 0.14), rgba(72, 77, 59, 0.501)); */
  background: linear-gradient(180deg, #244C82 0%, #0C2343 100%);
  border-radius: 4px;
}

.ul ul>li {
  background-color: rgb(24 55 97 / 80%);
}

.ul ul>li:hover {
  background-image: linear-gradient(to bottom right, rgba(205, 205, 205, 0.14), rgb(72 77 59));
}

.ul>ul>li:first-of-type {
  border-radius: 0;
}

.flow-icon {
  top: 10px;
}

.box {
  width: 100%;
  height: 160px;
  /* border-bottom: 1px solid #d4d7ce; */
  padding-top: 10px;
  margin-top: 20px;
  box-sizing: border-box;
}

.box:last-child {
  height: 60px;
  border-color: transparent;
}

/deep/ .el-switch__label {
  width: 240px !important;
  color: #fff;
  height: auto;
}

/deep/ .el-switch__label.is-active {
  color: #fff;
}

/deep/ .el-switch__label * {
  font-size: 32px;
}

.weather_ul {
  color: #fff;
  font-size: 24px;
  display: flex;
  justify-content: space-between;
  margin-top: 25px;
}

.weather_ul>li {
  height: 85px;
  display: inline-grid;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.weather_ul>li img {
  width: 50px;
}

/deep/ .el-switch__core {
  width: 50px !important;
}
</style>