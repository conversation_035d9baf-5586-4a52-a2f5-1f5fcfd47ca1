/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
import { isNumber } from 'lodash';
export function debounce(func, wait, immediate) {
    let timeout, args, context, timestamp, result

    const later = function() {
      // 据上一次触发时间间隔
      const last = +new Date() - timestamp

      // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
      if (last < wait && last > 0) {
        timeout = setTimeout(later, wait - last)
      } else {
        timeout = null
        // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
        if (!immediate) {
          result = func.apply(context, args)
          if (!timeout) context = args = null
        }
      }
    }

    return function(...args) {
      context = this
      timestamp = +new Date()
      const callNow = immediate && !timeout
      // 如果延时不存在，重新设定延时
      if (!timeout) timeout = setTimeout(later, wait)
      if (callNow) {
        result = func.apply(context, args)
        context = args = null
      }

      return result
    }
  }

export const numberFilter = (num, defaultNum) => {
  const defaultValue = defaultNum || '-';
  return isNumber(num) ? num : defaultValue;
}

//用于保证地图初始化完成后调用方法
export function MapInitTimer(...callbacks) {
  const that = this;
  let timer = setInterval(() => {
    if (top.window.gis) {
      clearInterval(timer);
      callbacks.forEach(callback => callback.call(that)); // 遍历并调用所有回调
    } else {
      return;
    }
  }, 3000);

  // 返回定时器ID，以便在外部可以清除定时器
  return timer;
}