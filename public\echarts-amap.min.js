!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("echarts")):"function"==typeof define&&define.amd?define(["echarts"],t):"object"==typeof exports?exports.amap=t(require("echarts")):(e.echarts=e.echarts||{},e.echarts.amap=t(e.echarts))}(window,function(e){return function(e){var t={};function o(n){if(t[n])return t[n].exports;var r=t[n]={i:n,l:!1,exports:{}};return e[n].call(r.exports,r,r.exports,o),r.l=!0,r.exports}return o.m=e,o.c=t,o.d=function(e,t,n){o.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.t=function(e,t){if(1&t&&(e=o(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(o.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)o.d(n,r,function(t){return e[t]}.bind(null,r));return n},o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,"a",t),t},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.p="",o(o.s=1)}([function(t,o){t.exports=e},function(e,t,o){o(0).registerCoordinateSystem("amap",o(2)),o(3),o(4),o(0).registerAction({type:"amapRoam",event:"amapRoam",update:"updateLayout"},function(e,t){t.eachComponent("amap",function(e){var t=e.getAMap(),o=t.getCenter();e.setCenterAndZoom([o.lng,o.lat],t.getZoom())})}),e.exports={version:"1.4.15-rc.1"}},function(e,t,o){var n=o(0);function r(e,t){this._amap=e,this.dimensions=["lng","lat"],this._mapOffset=[0,0],this._api=t}r.prototype.dimensions=["lng","lat"],r.prototype.setZoom=function(e){this._zoom=e},r.prototype.setCenter=function(e){this._center=this._amap.lnglatToPixel(e)},r.prototype.setMapOffset=function(e){this._mapOffset=e},r.prototype.getAMap=function(){return this._amap},r.prototype.dataToPoint=function(e){var t=new AMap.LngLat(e[0],e[1]),o=this._amap.lngLatToContainer(t),n=this._mapOffset;return[o.x-n[0],o.y-n[1]]},r.prototype.pointToData=function(e){var t=this._mapOffset;return[(e=this._amap.containerToLngLat({x:e[0]+t[0],y:e[1]+t[1]})).lng,e.lat]},r.prototype.getViewRect=function(){var e=this._api;return new n.graphic.BoundingRect(0,0,e.getWidth(),e.getHeight())},r.prototype.getRoamTransform=function(){return n.matrix.create()},r.dimensions=r.prototype.dimensions,r.create=function(e,t){var o,a=t.getDom();e.eachComponent("amap",function(e){var i=t.getZr().painter.getViewportRoot();if("undefined"==typeof AMap)throw new Error("AMap api is not loaded");if(o)throw new Error("Only one amap component can exist");if(!e.__amap){var p=a.querySelector(".ec-extension-amap");p&&(i.style.left="0px",i.style.top="0px",a.removeChild(p)),(p=document.createElement("div")).style.cssText="width:100%;height:100%",p.classList.add("ec-extension-amap"),a.appendChild(p);var s=e.get()||{};s=e.__options=n.util.clone(s);var c=e.__amap=new AMap.Map(p,s);(f=e.__layer=new AMap.CustomLayer(i)).setMap(c)}var f;c=e.getAMap();(f=e.getLayer()).hide();var u=c.getZoom(),m=c.getCenter();(o=new r(c,t)).setMapOffset(e.__mapOffset||[0,0]),o.setZoom(u),o.setCenter([m.lng,m.lat]),e.coordinateSystem=o,f.show()}),e.eachSeries(function(e){"amap"===e.get("coordinateSystem")&&(e.coordinateSystem=o)})},e.exports=r},function(e,t,o){e.exports=o(0).extendComponentModel({type:"amap",getAMap:function(){return this.__amap},getLayer:function(){return this.__layer},getMapOptions:function(){return this.__options},setCenterAndZoom:function(e,t){this.option.center=e,this.option.zoom=t},centerOrZoomChanged:function(e,t){var o,n,r=this.option;return o=e,n=r.center,!(o&&n&&o[0]===n[0]&&o[1]===n[1]&&t===r.zoom)},defaultOption:{center:[116.397475,39.908695],zoom:4}})},function(e,t,o){var n=o(0);e.exports=o(0).extendComponentView({type:"amap",render:function(e,t,o){var r=!0,a=e.getAMap(),i=o.getZr().painter.getViewportRoot(),p=e.coordinateSystem,s=function(t){if(!r){var n=i.parentNode.parentNode.parentNode,a=[-parseInt(n.style.left,10)||0,-parseInt(n.style.top,10)||0];i.style.left=a[0]+"px",i.style.top=a[1]+"px",p.setMapOffset(a),e.__mapOffset=a,o.dispatchAction({type:"amapRoam"})}};function c(){r||o.dispatchAction({type:"amapRoam"})}var f,u,m,d,l,y,h,g=(f=function(e){n.getInstanceByDom(o.getDom()).resize(),s.call(this,e)},u=300,m=a,h=function(){d=!1,l&&(y.apply(m,l),l=!1)},y=function(){d?l=arguments:(f.apply(m,arguments),setTimeout(h,u),d=!0)});a.off("zoomend",this._oldZoomEndHandler),a.off("moveend",this._oldZoomEndHandler),a.off("complete",this._oldZoomEndHandler),e.get("resizeEnable")&&a.off("resize",this._oldResizeHandler),a.on("zoomend",c),a.on("moveend",c),a.on("complete",c),e.get("resizeEnable")&&a.on("resize",g),this._oldZoomEndHandler=c,this._oldResizeHandler=g,r=!1}})}])});
//# sourceMappingURL=echarts-amap.min.js.map