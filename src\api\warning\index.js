import {request} from '@/utils/request'

// 预警接入接口
export function getYjjrList() {
  return request({
    url: '/ywtg-api/dp/ywtgYjSb/dpSb',
    method: 'get'
  })
}

// 预警分析接口
export function getYjfxList() {
  return request({
    url: '/ywtg-api/dp/ywtgYjSb/dpYjSbFx',
    method: 'get'
  })
}

// 物联手动设备预警筛选接口
export function getIotMaFilterList(data) {
  return request({
    url: '/ywtg-api/dp/ywtgYj/dpDtyj',
    method: 'post',
    data
  })
}

// 人员预警接口
export function getPeopleWarnList(data) {
  return request({
    url: '/ywtg-api/dp/ywtgYj/dpdtry',
    method: 'post',
    data
  })
}

// 多维感知列表接口
export function getDwgzList() {
  return request({
    url: '/ywtg-api/dp/ywtgYj/dpgzyj',
    method: 'post'
  })
}

// 预警级别
export function getWarningLevel() {
  return request({
    url: '/ywtg-api/dp/ywtgYj/dpYjzxYjjb',
    method: 'post'
  })
}