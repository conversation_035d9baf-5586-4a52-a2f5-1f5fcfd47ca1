const state = {
  markerArray: [],
  spsbCheckList: null,
  gzsbCheckList: null,
  warningCheckList: [],
  levelCheckList: []
}

const mutations = {
  ADD_MARKER: (state, markerId) => {
    state.markerArray.push(markerId)
  },
  DELETE_MARKER: (state, markerId) => {
    let newSet = new Set(state.markerArray)
    newSet.delete(markerId)
    state.markerArray = [...newSet]
  },
  CLEAR_MARKER: (state) => {
    state.markerArray = []
  },
  SPSB_HISTORY: (state, newArray) => {
    state.spsbCheckList = newArray
    // console.log('改变视频设备list后的结果', state.spsbCheckList)
  },
  GZSB_HISTORY: (state, newArray) => {
    state.gzsbCheckList = newArray
    // console.log('改变感知设备list后的结果', state.gzsbCheckList)
  },
  Warning_HISTORY: (state, newArray) => {
    state.warningCheckList = newArray
  },
  Level_HISTORY: (state, newArray) => {
    state.levelCheckList = newArray
  },
}

const actions = {
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
