<template>
  <div>
    <!-- 视频弹框 -->
    <CommonDialog
      :dialog-flag="showFlag"
      title="监控视频"
      :dialogWidth="'1600px'"
      @close="closeVideo"
      @open="openVideo"
    >
      <venueVideo
        :key="Key"
        ref="video"
        width="1600px"
        height="1100px"
        :visible="videoConfig.visible"
        :videoConfig="videoConfig"
        :code="code"
        :createflag="createflag"
        :isLogin="isLogin"
        :destoryflag="destoryflag"
      ></venueVideo>
    </CommonDialog>
  </div>
</template>


<script>
import CommonDialog from '@/components/Commondialog/index.vue' //弹框
import venueVideo from "@/components/Video/VenueVideo";
export default {
  name: "VideoPopup",
  props: {
    showFlag: {
      type: Boolean,
      default: false
    },
    videoKey: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      Key: 1,
      visible: false,
      videoConfig: {
        ctrlType: 'playerWin',
        ctrlCode: 'ctrl1',
        ctrlProperty: {
          displayMode: 1,
          splitNum: 1,
          channelList: [
            {
              channelId: '',
            },
          ],
        },
        visible: false,
        domId: 'domt1',
      },
      code: '',
      createflag: false,
      destoryflag: false,
      isLogin: false,
    }
  },
  components: {
    CommonDialog,
    venueVideo
  },
  computed: {},
  mounted() {

  },
  methods: {
    closeVideo() {
      let that = this
      this.destoryflag = false
      setTimeout(() => {
        that.destoryflag = true
      }, 100)
      this.videoConfig.visible = false
      this.$emit("closeVideo")
    },
    openVideo() {
      this.Key += 1
      this.code = this.videoKey
      this.videoConfig.ctrlProperty.channelList[0].channelId = this.videoKey
      let that = this
      this.createflag = false
      setTimeout(() => {
        that.createflag = true
      }, 100)
      this.videoConfig.visible = true
    }
  },
  watch: {}
}
</script>

<style scoped>

</style>