@charset "UTF-8";

.v-modal {
  width: 7680px !important;
  height: 2160px !important;
}

.mapDialog {
  width: 1592px !important;
  height: 985px;
  background: rgba(3, 24, 39, 0.88) !important;
  border: 1px solid #afdcfb;
  box-shadow: 0px 3px 35px 0px #000000 !important;
  border-radius: 50px !important;
  display: flex !important;
  flex-direction: column !important;
}

.mapDialog .el-dialog__header {
  height: 84px;
  background: linear-gradient(0deg, #073446, #00aae2);
  border-radius: 50px 50px 0 0;
  padding: 0 33px 0 55px;
}

.mapDialog .el-dialog__header .el-dialog__title {
  line-height: normal;
  font-size: 48px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0px 4px 16px rgba(0, 0, 0, 0.6);
  letter-spacing: 2px;
}

.mapDialog .el-dialog__header .el-dialog__headerbtn {
  color: #ffffff;
  font-size: 40px;
  top: 21px;
  right: 39px;
}

.mapDialog .el-dialog__header .el-dialog__headerbtn .el-dialog__close {
  color: #ffffff;
  font-size: 40px;
}

.mapDialog .el-dialog__body {
  flex: 1;
  /* background-color: antiquewhite; */
  box-sizing: border-box;
  padding: 44px 66px 69px 55px;
}

.mapDialog .el-dialog__body .bodyWrapp {
  width: 100%;
  height: 100%;
  /* background-color: #00aae2; */
  display: flex;
  box-sizing: border-box;
}

.mapDialog .el-dialog__body .bodyWrapp .contentLeft {
  width: 50%;
  height: 100%;
  /* background-color: chartreuse; */
  box-sizing: border-box;
  border-right: 2px solid rgba(5, 108, 146, 0.6);
  padding-right: 72px;

}

.mapDialog .el-dialog__body .bodyWrapp .contentLeft .leftInner {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  /* background-color: coral; */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
  overflow-y: auto;
}

.mapDialog .el-dialog__body .bodyWrapp .contentLeft .leftInner .subtitle {
  font-size: 38px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #f1de77;
  background: linear-gradient(180deg, #ffffff 0%, #cbf2ff 50.244140625%, #ffffff 53.0029296875%, #00c0ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.mapDialog .el-dialog__body .bodyWrapp .contentLeft .leftInner .thirdtitle {
  margin-top: 40px;
}

.mapDialog .el-dialog__body .bodyWrapp .contentLeft .leftInner .lineClass {
  display: flex;
  width: 100%;
  margin-top: 0px;
}

.mapDialog .el-dialog__body .bodyWrapp .contentLeft .leftInner .lineClass .lineLabelClass {
  width: 180px;
}

.mapDialog .el-dialog__body .bodyWrapp .contentLeft .leftInner .lineClass .lineLabelClass .labelStyle {
  font-size: 36px;
  font-family: Source Han Sans SC;
  font-weight: 400;
  color: #f1de77;
  background: linear-gradient(180deg, #ffffff 0%, #cbf2ff 50.244140625%, #ffffff 53.0029296875%, #00c0ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.mapDialog .el-dialog__body .bodyWrapp .contentLeft .leftInner .lineClass .lineContentClass {
  width: calc(100% - 180px);
  font-size: 36px;
  font-family: Source Han Sans SC;
  font-weight: 400;
  color: #d6e7f9;
}

.mapDialog .el-dialog__body .bodyWrapp .contentRight {
  width: 50%;
  height: 100%;
  /* background-color: chartreuse; */
  box-sizing: border-box;
  padding-left: 72px;
}

.mapDialog .el-dialog__body .bodyWrapp .contentRight .leftInner {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  /* background-color: coral; */
  display: flex;
  flex-direction: column;
  /* justify-content: space-between; */
  overflow: hidden;
  overflow-y: auto;
}

.mapDialog .el-dialog__body .bodyWrapp .contentRight .leftInner .subtitle {
  font-size: 38px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #f1de77;
  background: linear-gradient(180deg, #ffffff 0%, #cbf2ff 50.244140625%, #ffffff 53.0029296875%, #00c0ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.mapDialog .el-dialog__body .bodyWrapp .contentRight .leftInner .subtitle {
  font-size: 38px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #f1de77;
  background: linear-gradient(180deg, #ffffff 0%, #cbf2ff 50.244140625%, #ffffff 53.0029296875%, #00c0ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.mapDialog .el-dialog__body .bodyWrapp .contentRight .leftInner .timeLineWrap {
  display: flex;
  justify-content: center;
  margin-top: 42px;
}

.mapDialog .el-dialog__body .bodyWrapp .contentRight .leftInner .timeLineWrap .timeLine {
  display: flex;
  align-items: center;
}

.mapDialog .el-dialog__body .bodyWrapp .contentRight .leftInner .timeLineWrap .timeLine .activeColor {
  border: 3px solid #FFC460 !important;
}

.mapDialog .el-dialog__body .bodyWrapp .contentRight .leftInner .timeLineWrap .timeLine .activeBg {
  background: #FFC460 !important;

}

.mapDialog .el-dialog__body .bodyWrapp .contentRight .leftInner .timeLineWrap .timeLine .circleClass {
  width: 24px;
  height: 24px;
  background: #0C2A47;
  border: 3px solid #49A3FF;
  border-radius: 50%;
}

.mapDialog .el-dialog__body .bodyWrapp .contentRight .leftInner .timeLineWrap .timeLine .lineClass {
  width: 112px;
  height: 3px;
  background: #49A3FF;
}

.mapDialog .el-dialog__body .bodyWrapp .contentRight .leftInner .tagWrap {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  padding: 0 10px;
  margin-top: 34px;

}

.mapDialog .el-dialog__body .bodyWrapp .contentRight .leftInner .tagWrap .txtClass {
  font-size: 36px;
  font-family: Source Han Sans SC;
  font-weight: 400;
  color: #F1DE77;
  background: linear-gradient(180deg, #FFFFFF 0%, #CBF2FF 50.244140625%, #FFFFFF 53.0029296875%, #00C0FF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.mapDialog .el-dialog__body .bodyWrapp .contentRight .leftInner .tagWrap .activetxt {
  background: linear-gradient(180deg, #FFFFFF 0%, #FFECCB 50.244140625%, #FFFFFF 53.0029296875%, #FFC460 100%) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
}

.mapDialog .el-dialog__body .bodyWrapp .contentRight .leftInner .lineClass {
  display: flex;
  width: 100%;
  margin-top: 0px;
}

.mapDialog .el-dialog__body .bodyWrapp .contentRight .leftInner .fourthTitle {
  margin-top: 114px;
}

.mapDialog .el-dialog__body .bodyWrapp .contentRight .leftInner .mg {
  margin-top: 28px;
}

.mapDialog .el-dialog__body .bodyWrapp .contentRight .leftInner .lineClass .lineLabelClass {
  width: 180px;
}

.mapDialog .el-dialog__body .bodyWrapp .contentRight .leftInner .lineClass .lineLabelClass .labelStyle {
  font-size: 36px;
  font-family: Source Han Sans SC;
  font-weight: 400;
  color: #f1de77;
  background: linear-gradient(180deg, #ffffff 0%, #cbf2ff 50.244140625%, #ffffff 53.0029296875%, #00c0ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.mapDialog .el-dialog__body .bodyWrapp .contentRight .leftInner .lineClass .lineContentClass {
  width: calc(100% - 180px);
  font-size: 36px;
  font-family: Source Han Sans SC;
  font-weight: 400;
  color: #d6e7f9;
}

.mapDialog .el-dialog__body .bodyWrapp .contentRight .leftInner .lineClass .lineContentClass {
  width: calc(100% - 180px);
  font-size: 36px;
  font-family: Source Han Sans SC;
  font-weight: 400;
  color: #d6e7f9;
}

.mapDialog .el-dialog__body .bodyWrapp .contentRight .leftInner .lineClass .imgWrap {
  width: calc(100% - 180px);
}

.mapDialog .el-dialog__body .bodyWrapp .contentRight .leftInner .lineClass .imgWrap .imgClass {
  width: 379px;
  height: 213px;
  background-color: #f1de77;
}

