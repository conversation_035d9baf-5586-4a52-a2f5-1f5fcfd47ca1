<!--空间分析-->
<template>
  <div class="container">
    <div class="container-content" v-show="!childPanelShow">
      <div class="container-content-item" v-for="(item,i) in list" :key="i">
        <div class="container-content-item-top">
          <div class="container-content-item-name">{{item.name}}</div>
          <div class="container-content-item-btn" @click="clickFun(item,i)">{{item.status}}</div>
        </div>
      </div>
    </div>
    <!--淹没分析-->
    <InundationAnalysis v-if="childPanelShow && childPanelFunc == '淹没分析'" @back="childPanelClose"/>
    <!--通视分析-->
    <visibilityAnalysis v-if="childPanelShow && childPanelFunc == '通视分析'" @back="childPanelClose" />
    <!--场景特效-->
    <SceneEffects v-if="childPanelShow && childPanelFunc == '场景特效'" @back="childPanelClose" ref="effects"/>
    <div class="container-btn" @click="cleanall">移除空间分析结果</div>
  </div>
</template>

<script>
import InundationAnalysis from "@/components/mapComponent/MapFunComponents/SpatialAnalysisComponents/InundationAnalysis";
import visibilityAnalysis from "@/components/mapComponent/MapFunComponents/SpatialAnalysisComponents/visibilityAnalysis";
import SceneEffects from "@/components/mapComponent/MapFunComponents/SpatialAnalysisComponents/SceneEffects";
export default {
  name: "SpatialAnalysis",
  data() {
    return {
      childPanelShow: false,
      childPanelFunc:"",
      list: [
        {
          name:"淹没分析",
          status:"使用"
        },
        {
          name:"通视分析",
          status:"使用"
        },
        {
          name:"场景特效",
          status:"使用"
        }
      ],
    }
  },
  components: {
    InundationAnalysis,
    visibilityAnalysis,
    SceneEffects
  },
  computed: {},
  mounted() {

  },
  methods: {
    clickFun(item) {
      this.childPanelFunc = item.name
      this.childPanelShow = true
    },
    childPanelClose() {
      this.childPanelShow = false
      this.cleanall()
    },
    // 清除测量工具
    async cleanall() {
      //淹没分析
      top.window.rainFail?.stop()
      top.window.rainFail = null
      // this.baseSliderValue = 0
      // this.floodTimeSliderValue = 0

      //通视分析
      top.window.lineOfSightWidget?.destroy()
      top.window.lineOfSightWidget = null

      //场景特效模拟
      this.$refs.effects.removePointEffect('')
    },
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  .container-content {
    height: 1000px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    overflow-y: scroll;
    .container-content-item {
      width: 400px;
      margin-bottom: 15px;
      .container-content-item-top {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .container-content-item-name {
          color: #fff;
          font-size: 30px;
        }
        .container-content-item-btn {
          width: 70px;
          height: 35px;
          cursor: pointer;
          border: 1px solid hsla(0, 0%, 100%, 0.30980392156862746);
          box-sizing: border-box;
          transition: 0.3s;
          line-height: 30px;
          font-size: 25px;
          background: linear-gradient(180deg, hsla(0, 0%, 100%, 0.4), hsla(0, 0%, 100%, 0));
          color: white;
          text-align: center;
        }
        .container-content-item-btn:hover {
          background: #2960cb;
          color: white;
        }
      }
    }
  }
  .container-btn {
    width: 400px;
    height: 35px;
    cursor: pointer;
    border: 1px solid hsla(0, 0%, 100%, 0.30980392156862746);
    box-sizing: border-box;
    transition: 0.3s;
    line-height: 30px;
    font-size: 25px;
    background: linear-gradient(180deg, hsla(0, 0%, 100%, 0.4), hsla(0, 0%, 100%, 0));
    color: white;
    text-align: center;
    position: absolute;
    bottom: 30px;
  }
  .container-btn:hover {
    background: #2960cb;
    color: white;
  }
}
</style>