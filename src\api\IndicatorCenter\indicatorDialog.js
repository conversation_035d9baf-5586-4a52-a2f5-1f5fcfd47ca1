import { request } from '@/utils/request'

// 根据分类获取当前分类和子分类的最新指标值
export function getIndicatorData(params) {
  return request({
    url: '/ywtg-api/screen/zbzxDerivativeIndicatorHis/categoryZbz',
    method: 'get',
    params
  })
}
//指标详情
export function getIndicatorDetail(params) {
  return request({
    url: '/ywtg-api/screen/indicatorBackDetail/yscZbxq',
    method: 'get',
    params
  })
}

//指标详情图表
export function getIndicatorCharts(data) {
  return request({
    url: '/ywtg-api/screen/indicatorBackDetail/getIndicatorValueNew',
    method: 'post',
    data
  })
}
