<template>
  <div class="container">
    <div class="container-inner" :style="{width: width +'px'}">
      <div class="container-inner-item" v-for="(item,i) in keyDesc" :key="i" :class="{isHalf:item.isHalf}" :style="{width: item.isHalf?width/2 + 'px':width + 'px'}">
        <div class="key">{{item.label}}</div>
        <div class="value" :title="detailData[item.key]" v-if="item.isHtml" v-html="detailData[item.key]" />
        <div class="value" :title="detailData[item.key]" v-else>{{detailData[item.key]}}</div>
      </div>
      <img :src="videoIcon" alt="" style="width: 120px;height: 122px;cursor: pointer" v-if="detailData.videoCode" @click="openVideo">
    </div>
  </div>
</template>

<script>
export default {
  name: "index",
  props: {
    detailData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    keyDesc: {
      type: Array,
      default: () => {
        return []
      }
    },
    width: {
      type: Number,
      default: () => {
        return 900
      }
    }
  },
  data() {
    return {
      videoIcon:require("@/assets/common/videoIcon.png")
    }
  },
  computed: {},
  mounted() {

  },
  methods: {
    openVideo() {
      this.$EventBus.$emit("openVideo", this.detailData.videoCode)
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .container {
    width: 100%;
    height: 100%;
    .container-inner {
      margin: 32px;
      max-height: 1700px;
      overflow-y: scroll;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      .container-inner-item {
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        margin-bottom: 16px;
        .key {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: 32px;
          color: #CEF2FF;
          line-height: 40px;
          text-align: left;
          font-style: normal;
          white-space: nowrap;
        }
        .value {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: 32px;
          color: #FFFFFF;
          line-height: 40px;
          text-align: left;
          font-style: normal;
          margin-left: 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          padding-right: 10px;
        }
      }
      .isHalf {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
</style>