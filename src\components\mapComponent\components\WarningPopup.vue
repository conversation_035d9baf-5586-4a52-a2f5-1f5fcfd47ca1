<template>
  <div class="warningDialogWrap">
    <div class="warningWrapp">
      <div class="line">
        <div class="keyName">预警名称：</div>
        <div class="value">{{info.name}}</div>
      </div>
      <div class="line">
        <div class="keyName">预警类型：</div>
        <div class="value">{{info.type}}</div>
      </div>
      <div class="line">
        <div class="keyName">预警时间：</div>
        <div class="value">{{info.time}}</div>
      </div>
      <div class="line">
        <div class="keyName">预警内容：</div>
        <div class="value">{{info.content}}</div>
      </div>
      <div class="line">
        <div class="keyName">现场图片：</div>
        <img :src="info.img" alt="">
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "WarningPopup",
  data () {
    return {
      info: {
        name: "人员超限",
        type: "橙色预警",
        time: "2023/03/09 12:57:33",
        content: "大面积人员聚集",
        img: require("@/assets/common/pic.png")
      }
    }
  },
  methods: {

  }
}
</script>

<style scoped lang="less">
  .warningDialogWrap {
    .warningWrapp {
      width: 100%;
      height: 100%;
      margin: 60px;
      display: flex;
      justify-content: space-evenly;
      align-items: flex-start;
      flex-direction: column;
      .line {
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        height: fit-content;
        margin-bottom: 20px;
        .keyName {
          font-size: 36px;
          font-family: Source Han Sans SC;
          font-weight: 400;
          color: #f1de77;
          background: linear-gradient(180deg, #ffffff 0%, #00c0ff 50.244140625%, #ffffff 53.0029296875%, #cbf2ff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .value {
          font-size: 36px;
          font-family: Source Han Sans SC;
          font-weight: 400;
          color: #d6e7f9;
        }
        img {
          width: 379px;
          height: 213px;
        }
      }
    }
  }
</style>