import { request } from '@/utils/request'

// irs接口
export function getIrsData(data) {
  return request({
    url: '/actuator',
    method: 'post',
    data
  })
}

// 开发区irs接口
export function getKfqIrsData(data) {
  return request({
    url: '/ywtg-api/irs/KfqActuator',
    method: 'post',
    data
  })
}

// assembly接口
export function getAssemblyData(data) {
  return request({
    url: '/assembly',
    method: 'post',
    data
  })
}


// 驾驶舱获取指定六项指标
export function getJschqzdqyzf6() {
  return request({
    url: '/ywtg-api/screen/zbzxDerivativeIndicatorHis/jschqzdqyzf6',
    method: 'get'
  })
}

// 高素质人才
export function getJschqzdqyzfgsy() {
  return request({
    url: '/ywtg-api/screen/zbzxDerivativeIndicatorHis/jschqzdqyzfgsy',
    method: 'get'
  })
}
