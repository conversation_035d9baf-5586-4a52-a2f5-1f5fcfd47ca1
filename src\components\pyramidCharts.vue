<template>
  <div class="charts" :id="'pyramidCharts' + id"></div>
</template>

<script>
export default {
  name: "pyramidCharts",
  data() {
    return {}
  },
  props:{
    id:{
      type:String,
      default:"",
      require:true
    },
    // {name:"第一产业",value:0}
    chartsData:{
      type:Array,
      default:() => [],
      require: true
    }
  },
  computed: {},
  mounted() {
    this.initPyramidCharts()
  },
  methods: {
    initPyramidCharts() {
      let sum = 0
      this.chartsData.forEach((item,i) => {
        sum += item.value
      })
      let colorList = ['#01AFEB','#F9BB5B','#F38230'];
      let myChart = this.$echarts.init(document.getElementById("pyramidCharts" + this.id));
      let option = {
        color:colorList,
        tooltip: {
          trigger: "item",
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        series: [
          {
            name: '',
            type: 'funnel',
            left: '0%',
            width: '50%',
            sort: 'ascending',
            gap: 8,
            label: {
              normal: {
                formatter: function (params) {
                  return params.name + '  全市占比:' + ((params.value / sum)*100).toFixed(0) + "%"
                },
                fontSize: 28,
              },
            },
            labelLine: {
              normal: {
                show: false
              }
            },
            itemStyle: {
              normal: {
                opacity: .8,
                borderColor: 'rgba(9,20,36,0)',
              }
            },
            data: this.chartsData
          },
          {
            name: '',
            type: 'funnel',
            left: '0%',
            width: '50%',
            maxSize: '50%',
            sort: 'ascending',
            gap: 8,
            label: {
              normal: {
                show: false,
              },
            },
            itemStyle: {
              normal: {
                opacity: 1,
                borderColor: 'rgba(9,20,36,0)',
                borderWidth: 0,
                shadowBlur: 5,
                shadowOffsetX: 5,
                shadowOffsetY: 0,
                shadowColor: 'rgba(0, 0, 0, 1)',
              }
            },

            data: this.chartsData
          }
        ]
      };
      myChart.setOption(option)
      myChart.getZr().on('mousemove', param => {
        myChart.getZr().setCursorStyle('default')
      })
    }
  },
  watch: {}
}
</script>

<style scoped>
  .charts {
    width: 700px;
    height: 423px;
  }
</style>