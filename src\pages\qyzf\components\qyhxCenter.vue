<template>
  <div class="qyhx-center-container">
    <div class="qyhx-center-top">
      <div class="qyhx-center-select-box">
        <div class="select-item" v-for="(ele, i) in selectData" :key="i">
          <div class="select-item-wrap" v-if="ele.children && ele.children.length > 0">
            <div class="select-name">{{ ele.name }} :</div>
            <div :class="['select-list', ele.flag ? '' : 'remove-more']">
              <ul class="ul-list">
                <li v-for="(item, index) in ele.children" :key="index">
                  <span>{{ item }}</span>
                </li>
              </ul>
            </div>
            <div class="show-more" @click="showMoreFun(ele)">{{ ele.flag ? '收回' : '更多' }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="center">
      <div class="dialog-con">
        <div class="con-right">
          <div class="company-tab">
            <div
              class="tab-item"
              v-for="(item, index) in tabList"
              @click="clickTab(index)"
              :class="tabIndex == index ? 'tab-active' : ''"
              :key="index"
            >
              <div class="item-bg"></div>
              <div class="item-name">{{ item }}</div>
            </div>
          </div>
          <div class="detail">
            <!-- 基本信息 -->
            <div class="companyJbxx" v-if="tabIndex == 0">
              <el-collapse v-model="activeName" accordion>
                <el-collapse-item v-for="(item, index) in jbxxTitle" :name="index" :key="index">
                  <template slot="title">
                    <div :class="['collapse-item', activeName === index ? 'collapseActive' : '']">
                      <span>{{ item }}</span>
                    </div>
                  </template>
                  <div v-if="index === 0" style="margin-bottom: 30px">
                    <div class="jbxx-title">
                      <div class="img">{{ personName }}</div>
                      <div class="info">
                        <p>法定代表人：{{ personName }}</p>
                        <p>统一信用代码: {{ allMessage[0].tyshxydm }}</p>
                      </div>
                    </div>
                    <el-descriptions class="margin-top" :column="2" :size="size" border>
                      <el-descriptions-item v-for="(item, index) in jbxxList" :key="index">
                        <template slot="label">{{ item.name }}</template>
                        {{ item.value }}
                      </el-descriptions-item>
                    </el-descriptions>
                  </div>
                  <!-- 其他样式 -->
                  <div v-else v-for="(el, i) in qygmxx" style="margin: 20px 0 30px 0" :key="i">
                    <div v-show="item === el.title" class="table" v-if="el.tableData.length != 0 && el.style == 'list'">
                      <div class="th">
                        <div
                          class="th_td"
                          v-for="(value, key, index) in el.tableData[0]"
                          style="flex: 0.2"
                          :title="keyName[key]"
                          :key="index"
                        >
                          {{ keyName[key] }}
                        </div>
                      </div>
                      <div class="tbody" v-if="el.tableData.length != 0">
                        <div class="tr" v-for="(value, key, index) in el.tableData" :key="index">
                          <div class="tr_td" style="flex: 0.2" v-for="(v, k, i) in value" :title="v" :key="i">
                            {{ v }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
            <!-- 股东信息 -->
            <!-- <div class="companyGdxx" style="position: relative" v-else-if="tabIndex == 1">
              <el-collapse v-model="activeName" accordion>
                <el-collapse-item
                  v-for="(item, index) in gdxxList"
                  :name="index"
                  @click.native="changeItem(item)"
                  :key="index"
                >
                  <template slot="title">
                    <div :class="['collapse-item', activeName === index ? 'collapseActive' : '']">
                      <span>{{ item.title }}</span>
                    </div>
                  </template>
                  <div v-if="item.title == '企业股权穿透'">
                    <div id="appc1" style="height: 1200px"></div>
                  </div>
                  <div v-if="item.title == '股东关联企业'">
                    <div id="appc1" style="height: 850px"></div>
                  </div>
                  <div v-if="item.title == '对外投资分析'" style="margin-bottom: 200px">
                    <div class="s-flex s-row-around s-flex-wrap s-m-t-30">
                      <div v-for="(ec, index) in item.tableTitle" :key="index">
                        <header class="item_title">
                          <span>{{ ec }}</span>
                        </header>
                        <div :id="'echarts_dwtz' + (index + 1)" style="width: 1500px; height: 600px"></div>
                      </div>
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div> -->
            <!-- 变更信息 -->
            <div class="companyGdxx" v-else-if="tabIndex == 2">
              <el-collapse v-model="activeName" accordion>
                <el-collapse-item :name="0">
                  <template slot="title">
                    <div :class="['collapse-item', activeName === index ? 'collapseActive' : '']">
                      <span>变更信息</span>
                    </div>
                  </template>
                  <div style="margin: 20px 0 30px 0">
                    <div class="table">
                      <div class="th">
                        <div class="th_td" v-for="(el, i) in qybgTitle" style="flex: 0.25" :key="i">{{ el }}</div>
                      </div>
                      <div class="tbody">
                        <div class="tr" v-for="(el, i) in qybgData" :key="i">
                          <div class="tr_td" style="flex: 0.25" :title="el.bgrq">{{ el.bgrq }}</div>
                          <div class="tr_td" style="flex: 0.25" :title="el.bgsx_zw">{{ el.bgsx_zw }}</div>
                          <div class="tr_td" style="flex: 0.25" :title="el.bgqnr">{{ el.bgqnr }}</div>
                          <div class="tr_td" style="flex: 0.25" :title="el.bghnr">{{ el.bghnr }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
            <!-- 知识产权 -->
            <div v-else-if="tabIndex == 4">
              <el-collapse v-model="activeName" accordion>
                <el-collapse-item v-for="(item, index) in zscqList" :name="index" :key="index">
                  <template slot="title">
                    <div :class="['collapse-item', activeName === index ? 'collapseActive' : '']">
                      <span>{{ item.title }}</span>
                    </div>
                  </template>
                  <div v-if="item.style == 'showEcharts' && item.title.includes('专利')">
                    <header class="item_title s-m-x-auto" style="margin-left: 1400px"><span>科技创新</span></header>
                    <div class="s-flex s-row-evenly">
                      <img src="@/pages/qyzf/img/zscq_icon.png" alt="" />
                      <span class="s-font-50 s-c-blue-gradient s-w7" style="margin: 0 20px">35.25T</span>
                      <span class="kj_tip">
                        备注：“T”是技术含量单位，以上科技创新总含量是根据企业发明公布专利，发明授权专利，实用新型专利，外观设计专利四项进行单位换算出来，仅作为参考
                      </span>
                    </div>
                    <div class="s-flex s-row-around s-flex-wrap s-m-t-30">
                      <div v-for="(ec, index) in item.tableTitle" :key="index">
                        <header class="item_title">
                          <span>{{ ec }}</span>
                        </header>
                        <div :id="'echarts_zscq' + (index + 1)" style="width: 1500px; height: 600px"></div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="s-flex s-row-around s-flex-wrap"
                    v-if="item.style == 'showEcharts' && item.title.includes('商标')"
                  >
                    <div v-for="(ec, index) in item.tableTitle" :key="index">
                      <header class="item_title">
                        <span>{{ ec }}</span>
                      </header>
                      <div :id="'echarts_zscqSb' + (index + 1)" style="width: 1100px; height: 600px"></div>
                    </div>
                  </div>
                  <div style="margin: 20px 0 30px 0" v-show="item.style == 'table'">
                    <div v-if="item.tableTab">
                      <div v-for="(ele, i) in item.tableTab" :key="i">
                        <header class="item_title s-m-b-15">
                          <span>{{ ele.name }}</span>
                        </header>
                        <div class="table" style="max-height: 480px">
                          <div class="th" style="position: sticky; top: 0">
                            <div class="th_td" v-for="(el, i) in ele.tableTitle" style="flex: 0.25" :key="i">
                              {{ el }}
                            </div>
                          </div>
                          <div class="tbody">
                            <div class="tr" v-if="ele.tableData.length == 0">暂无数据</div>
                            <div class="tr" v-for="(el, i) in ele.tableData" :key="i">
                              <div class="tr_td" style="flex: 0.25">{{ i }}</div>
                              <div class="tr_td" style="flex: 0.25" v-for="(val, i) in el" :key="i">{{ val }}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="table" v-if="!item.tableTab" style="max-height: 900px">
                      <div class="th" style="position: sticky; top: 0">
                        <div class="th_td" style="flex: 0.25">序号</div>
                        <div class="th_td" v-for="(el, i) in item.tableTitle" style="flex: 0.25" :key="i">{{ el }}</div>
                      </div>
                      <div class="tbody">
                        <div class="tr" v-if="item.tableData.length == 0">暂无数据</div>
                        <div class="tr" v-for="(el, i) in item.tableData" :key="i">
                          <div class="tr_td" style="flex: 0.25">{{ i + 1 }}</div>
                          <div
                            :class="['tr_td', inx == 1 && 'zscq_c_blue']"
                            :style="{
                              flex: 0.25,
                              color: val == '已注册' ? '#0BFFFE' : inx == 3 && item.title.includes('商标') && 'red',
                            }"
                            v-for="(val, key, inx) in el"
                            :key="inx"
                          >
                            {{ val }}
                          </div>
                          <div
                            class="th_td zscq_c_blue"
                            style="flex: 0.25"
                            v-if="item.title != '著作权'"
                            @click="openMag(el, item)"
                          >
                            详情
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
            <!-- 司法案件 -->
            <div v-else-if="tabIndex == 6">
              <el-collapse v-model="activeName" accordion>
                <el-collapse-item v-for="(label, index) in sfajTitle" :name="index" :key="index">
                  <template slot="title">
                    <div :class="['collapse-item', activeName === index ? 'collapseActive' : '']">
                      <span>{{ label }}</span>
                      <span class="sfajBack" @click.stop="toBack()" v-show="showSfajDetail">
                        <img src="@/pages/qyzf/img/qyhx-back.png" alt="" />
                        返回
                      </span>
                    </div>
                  </template>
                  <div style="margin: 20px 0 30px 0">
                    <div class="sfajList" v-show="!showSfajDetail">
                      <div class="sfajItem" v-for="(item, i) in sfajData" :key="i">
                        <p class="sfajItem_title">
                          {{ item.actionCause }}
                          <span class="to_detail" @click="toDetail(item)">
                            案件进展
                            <img
                              src="@/pages/qyzf/img/to_detail.png"
                              alt=""
                              style="position: relative; top: 5px; left: 5px"
                            />
                          </span>
                        </p>
                        <p class="sfajItem_tags"></p>
                        <p class="sfajItem_text">
                          <span>案号：{{ item.caseCode }}</span>
                          <span>审理法院：{{ item.courtName }}</span>
                        </p>
                      </div>
                    </div>
                    <div class="ajsllcList" v-show="showSfajDetail">
                      <div class="ajsllc_title sfajItem_title">
                        {{ ajsllcData.article }}
                      </div>
                      <p style="margin-top: 20px">
                        <span class="ajsllc_name">案号：</span>
                        <span class="ajsllc_value">{{ ajsllcData.caseCode }}</span>
                      </p>
                      <p>
                        <span class="ajsllc_name">原告：</span>
                        <span class="ajsllc_value">{{ ajsllcData.accuser }}</span>
                      </p>
                      <p>
                        <span class="ajsllc_name">被告：</span>
                        <span class="ajsllc_value">{{ ajsllcData.defendant }}</span>
                      </p>
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
            <!-- 其他 -->
            <div class="companyGdxx" v-else>
              <div class="s-flex s-row-around s-m-b-20" v-if="tabIndex == 5">
                <div v-for="(item, index) in qyfxData" :key="index">
                  <div :id="'echarts_fx' + (index + 1)" style="width: 1100px; height: 700px"></div>
                  <p class="s-font-38 s-c-white s-w7 s-text-center">
                    <span class="qyhx-center-tit_box">
                      {{ item.name }}
                      <span
                        :style="{
                          color: index == 0 ? '#d03a40' : index == 1 ? '#de9042' : '#6fb446',
                          marginLeft: '20px',
                        }"
                      >
                        {{ item.value }}
                      </span>
                    </span>
                  </p>
                </div>
              </div>
              <el-collapse v-model="activeName" accordion>
                <el-collapse-item v-for="(item, index) in allData" :name="index" :key="index">
                  <template slot="title">
                    <div :class="['collapse-item', activeName === index ? 'collapseActive' : '']">
                      <span>{{ item.title }}</span>
                    </div>
                  </template>
                  <div style="margin: 20px 0 30px 0">
                    <div v-if="item.style == 'showEcharts'">
                      <div style="display: flex; justify-content: right">
                        <div
                          v-for="(seEl, seIndex) in item.tableTitle"
                          :class="['qyhx-center-tab_item', secondIndex === seIndex ? 'tab_active' : '']"
                          style="margin-left: 20px"
                          @click="changeTab(seIndex)"
                          :key="seIndex"
                        >
                          {{ seEl }}
                        </div>
                      </div>
                      <div id="qyhx-cwxx" style="height: 500px; width: 3332px"></div>
                    </div>
                    <div v-if="item.unit" style="font-size: 35px; color: #fff">单位：万元</div>
                    <div class="table" v-if="item.style == 'table'">
                      <div class="th" style="justify-content: normal">
                        <div class="th_td" v-for="(el, i) in item.tableTitle" style="flex: 0.2" :title="el" :key="i">
                          {{ el }}
                        </div>
                      </div>
                      <div class="tbody" v-if="item.tableData.length != 0">
                        <div class="tr" v-for="(el, i) in item.tableData" :key="i">
                          <div class="tr_td s-w7" style="flex: 0.2">{{ i + 1 }}</div>
                          <div class="tr_td" style="flex: 0.2" :title="el.value1">{{ el.value1 }}</div>
                          <div class="tr_td" style="flex: 0.2" :title="el.value2">{{ el.value2 }}</div>
                          <div class="tr_td" style="flex: 0.2" :title="el.value3">{{ el.value3 }}</div>
                          <div class="tr_td" style="flex: 0.2" :title="el.value4">{{ el.value4 }}</div>
                          <div class="tr_td" style="flex: 0.2" v-if="el.value5" :title="el.value5">
                            {{ el.value5 }}
                          </div>
                        </div>
                      </div>
                    </div>
                    <!-- 其他样式 -->
                    <div class="table" v-if="item.tableData.length != 0 && item.style == 'list'">
                      <div class="th">
                        <div
                          class="th_td"
                          v-for="(value, key, index) in item.tableData[0]"
                          style="flex: 0.2"
                          :title="keyName[key]"
                          :key="index"
                        >
                          {{ keyName[key] }}
                        </div>
                      </div>
                      <div class="tbody" v-if="item.tableData.length != 0">
                        <div class="tr" v-for="(value, key, index) in item.tableData" :key="index">
                          <div class="tr_td" style="flex: 0.2" v-for="(v, k, i) in value" :title="v" :key="i">
                            {{ v }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import $ from 'jquery'
import axios from 'axios'
import d3 from '@/pages/qyzf/js/d3.js'
import { getCsdnInterface, getCsdnInterface2 } from '@/api/csdnIndexApi'
import { getIrsData, getAssemblyData, getKfqIrsData } from '@/api/qyzf/qyhx.js'
export default {
  props: {
    allMessage: {
      type: Array,
      default: () => [],
    },
  },
  components: {},
  data() {
    return {
      d3: d3,
      listDataChildren: {
        id: 'abc1005',
        // 根节点名称
        name: '浙江尖峰集团股份有限公司',
        // 子节点列表
        children: [],
      },
      listDataParent: {
        id: 'abc1005',
        // 根节点名称
        name: '浙江尖峰集团股份有限公司',
        // 父节点列表
        parents: [],
      },
      activeName: 0,
      jbxxTitle: ['基本信息', '高新技术企业信息', '社保缴纳'],
      secondIndex: 0,
      chartScreen: null,
      setOption: null,
      name: '--',
      fr: '--',
      gsBq: [],
      gsBq2: [],
      cyl: [],
      cylList: [],
      // 拓扑图数据
      tytData: [],
      chartData: {
        name: '北京摩拜科技有限公司',
        value: 1,
        children: [
          { name: '基本信息', value: '1', children: [] },
          { name: '股东信息', value: '1', children: [] },
          { name: '变更信息', value: '1', children: [] },
          { name: '知识产权', value: '1', children: [] },
          { name: '知识产权', value: '1', children: [] },
          { name: '知识产权', value: '1', children: [] },
        ],
      },
      // 拓扑图的数据
      tptData: [],
      // 其他数据
      qygmxx: [
        {
          title: '高新技术企业信息',
          style: 'list',
          tableTitle: [],
          tableData: [],
        },
        {
          title: '社保缴纳',
          style: 'list',
          tableTitle: [],
          tableData: [],
        },
      ],
      companyIndx: 0,
      tabIndex: 0,
      tabList: [
        '基本信息',
        // '股东信息',
        '经营情况',
        '企业变更',
        '信用评价',
        '知识产权',
        '企业风险',
        '司法案件',
        '安全生产',
      ],
      size: '',
      jbxxList: [],
      name: '',
      personName: '',
      personId: '',
      gdxxList: [
        {
          title: '企业股权穿透',
          style: 'showEcharts',
          tableTitle: [],
          tableData: [],
        },
      ],
      // 财务信息
      cwData: [
        {
          title: '经营信息',
          style: 'showEcharts',
          tableTitle: ['净利润', '利润总额', '销售收入', '纳税总额', '资产总计', '负债总额'],
          tableData: [],
        },
        {
          title: '财务信息',
          style: 'list',
          unit: '万元',
          tableTitle: [],
          tableData: [],
        },

        {
          title: '财政补助企业兑现详情信息',
          style: 'list',
          tableTitle: [],
          tableData: [],
        },
      ],
      // 经营异常
      jyycData: [],
      // 企业变更
      qybgTitle: ['变更日期', '变更事项', '变更前内容', '变更后内容'],
      qybgData: [],
      xypjList: [
        {
          title: '企业公共信用评价信息',
          style: 'list',
          tableTitle: [],
          tableData: [],
        },
        {
          title: '金华市金阳光惠农惠企-企业信息',
          style: 'list',
          tableTitle: [],
          tableData: [],
        },
        {
          title: '金华市区规下工业企业亩产效益综合评价结果信息',
          style: 'list',
          tableTitle: [],
          tableData: [],
        },
        {
          title: '劳动保障诚信等级信息',
          style: 'list',
          tableTitle: [],
          tableData: [],
        },
        {
          title: '企业环境行为信用评价信息',
          style: 'list',
          tableTitle: [],
          tableData: [],
        },
      ],
      sfajTitle: ['司法案件'],
      sfajData: [],
      showSfajDetail: false,
      ajsllcData: {
        title: '李璐、邵阳市鹿山房地产开发有限公司商品房销售合同纠纷民事一审民事判决书',
        ah: '(2021)湘0511民初849号',
        accuser: '陈**',
        defendant: '啊理金华技术运营有限公司',
      },
      activities: [
        {
          name: '开庭公告',
          timestamp: '2023-12-27',
          content: [
            {
              name: '法庭',
              value: '广西壮族自治区高级人民法院',
            },
          ],
        },
        {
          name: '法院公告',
          timestamp: '2023-12-28',
        },
        {
          name: '限制高消费',
          timestamp: '2023-12-29',
          content: [
            {
              name: '限制对象',
              value: '王新',
            },
            {
              name: '申请人',
              value: '李闯',
            },
          ],
        },
        {
          name: '终本案件',
          timestamp: '2023-12-30',
          content: [
            {
              name: '被执行公司',
              value: '啊理金华技术运营有限公司',
            },
            {
              name: '执行标的',
              value: '8724421元',
            },
            {
              name: '被执行人',
              value: '王新',
            },
            {
              name: '执行标的',
              value: '8724421元',
            },
          ],
        },
      ],
      aqscList: [
        {
          title: '重大火灾隐患信息',
          style: 'list',
          tableTitle: [],
          tableData: [],
        },
        {
          title: '危化品运输企业信息',
          style: 'list',
          tableTitle: [],
          tableData: [],
        },
        {
          title: '涉爆粉尘企业特性信息',
          style: 'list',
          tableTitle: [],
          tableData: [],
        },
        {
          title: '行政强制信息',
          style: 'list',
          tableTitle: [],
          tableData: [],
        },
        {
          title: '危化品生产企业',
          style: 'list',
          tableTitle: [],
          tableData: [],
        },
        {
          title: '工矿商贸领域生产安全事故信息',
          style: 'list',
          tableTitle: [],
          tableData: [],
        },
        {
          title: '特种设备企业风险信息',
          style: 'list',
          tableTitle: [],
          tableData: [],
        },
      ],
      zscqList: [
        {
          title: '专利概览',
          style: 'showEcharts',
          tableTitle: ['专利类型', '专利法律状态', '专利申请趋势', '专利发布趋势'],
          tableData: [
            [
              { name: '实用新型', value: 33 },
              { name: '外观设计', value: 23 },
              { name: '发明公布', value: 12 },
              { name: '发明授权', value: 6 },
            ],
            [
              { name: '授权', value: 33 },
              { name: '未缴纳专利权终止', value: 23 },
              { name: '公布视为驳回', value: 12 },
              { name: '实质申请', value: 67 },
              { name: '发布驳回', value: 26 },
            ],
            [
              {
                name: '实用新型',
                list: [
                  { time: '2019', value: 33 },
                  { time: '2020', value: 25 },
                  { time: '2021', value: 43 },
                  {
                    time: '2022',
                    value: 43,
                  },
                  { time: '2023', value: 98 },
                ],
              },
              {
                name: '外观设计',
                list: [
                  { time: '2019', value: 13 },
                  { time: '2020', value: 35 },
                  { time: '2021', value: 23 },
                  {
                    time: '2022',
                    value: 52,
                  },
                  { time: '2023', value: 56 },
                ],
              },
              {
                name: '发明公布',
                list: [
                  { time: '2019', value: 23 },
                  { time: '2020', value: 45 },
                  { time: '2021', value: 24 },
                  {
                    time: '2022',
                    value: 26,
                  },
                  { time: '2023', value: 54 },
                ],
              },
              {
                name: '发明授权',
                list: [
                  { time: '2019', value: 63 },
                  { time: '2020', value: 55 },
                  { time: '2021', value: 64 },
                  {
                    time: '2022',
                    value: 22,
                  },
                  { time: '2023', value: 35 },
                ],
              },
            ],
            [
              {
                name: '实用新型',
                list: [
                  { time: '2019', value: 33 },
                  { time: '2020', value: 65 },
                  { time: '2021', value: 74 },
                  {
                    time: '2022',
                    value: 75,
                  },
                  { time: '2023', value: 86 },
                ],
              },
              {
                name: '外观设计',
                list: [
                  { time: '2019', value: 23 },
                  { time: '2020', value: 85 },
                  { time: '2021', value: 23 },
                  {
                    time: '2022',
                    value: 86,
                  },
                  { time: '2023', value: 53 },
                ],
              },
              {
                name: '发明公布',
                list: [
                  { time: '2019', value: 53 },
                  { time: '2020', value: 25 },
                  { time: '2021', value: 52 },
                  {
                    time: '2022',
                    value: 43,
                  },
                  { time: '2023', value: 89 },
                ],
              },
              {
                name: '发明授权',
                list: [
                  { time: '2019', value: 63 },
                  { time: '2020', value: 15 },
                  { time: '2021', value: 15 },
                  {
                    time: '2022',
                    value: 87,
                  },
                  { time: '2023', value: 12 },
                ],
              },
            ],
          ],
        },
        {
          title: '专利列表',
          style: 'table',
          tableTitle: [
            '发明名称',
            '专利类型',
            '法律情况',
            '申请号',
            '申请日期',
            '公开（公告）号',
            '公开日期',
            '发明人',
            '详细信息',
          ],
          tableData: [],
        },
        {
          title: '商标概况',
          style: 'showEcharts',
          tableTitle: ['商标申请年份趋势', '商标国际分类分布', '商标申请状态'],
          tableData: [
            [
              { time: '2011', value: 1 },
              { time: '2015', value: 1 },
              { time: '2016', value: 5 },
              {
                time: '2017',
                value: 18,
              },
              { time: '2018', value: 14 },
              { time: '2020', value: 28 },
            ],
            [
              { name: '9类 科学仪器', value: 23.88 },
              { name: '35类 广告销售', value: 8.96 },
              { name: '42类 设计研究', value: 8.96 },
              { name: '12类 运输工具', value: 5.97 },
              { name: '30类 方便食品', value: 5.97 },
              { name: '38类 通讯服务', value: 5.97 },
              { name: '39类 运输储藏', value: 5.97 },
              { name: '37类 建筑修理', value: 4.48 },
            ],
            [
              { name: '制造业', value: 65.67 },
              { name: '采矿业', value: 34.33 },
            ],
          ],
        },
        {
          title: '商标列表',
          style: 'table',
          tableTitle: [
            '商标图案',
            '商标名称',
            '国际分类',
            '商标状态',
            '申请/注册号',
            '申请日期',
            '注册公告日期',
            '内容',
          ],
          tableData: [],
        },
        {
          title: '著作权',
          style: 'table',
          tableTitle: [],
          tableData: [],
          tableTab: [
            {
              name: '作品著作权',
              tableTitle: ['序号', '作品名称', '作品类型', '首次发表日期', '创作完成日期', '登记号', '登记日期'],
              tableData: [],
            },
            {
              name: '软件著作权',
              tableTitle: [
                '序号',
                '软件全称',
                '软件简称',
                '版本号',
                '登记号',
                '开发完成日期',
                '首次发布日期',
                '登记日期',
                '权力取得方式',
              ],
              tableData: [],
            },
          ],
        },
      ],
      fxyqList: [
        {
          title: '拖欠工资黑名单信息',
          style: 'list',
          tableTitle: [],
          tableData: [],
        },
        {
          title: '严重违法企业信息',
          style: 'list',
          tableTitle: [],
          tableData: [],
        },
        {
          title: '劳动保障处罚信息',
          style: 'list',
          tableTitle: [],
          tableData: [],
        },
        {
          title: '环境违法失信黑名单信息',
          style: 'list',
          tableTitle: [],
          tableData: [],
        },
        {
          title: '企业经营异常名录信息',
          style: 'list',
          tableTitle: [],
          tableData: [],
        },
        {
          title: '金融风险企业名单',
          style: 'list',
          tableTitle: [],
          tableData: [],
        },
        {
          title: '失信信息',
          style: 'list',
          tableTitle: [],
          tableData: [],
        },
        {
          title: '行政处罚信息',
          style: 'list',
          tableTitle: [],
          tableData: [],
        },
      ],
      keyName: {},
      // 企业风险
      qyfxList: [
        {
          title: '风险信息',
          style: 'table',
          tableTitle: ['序号', '风险类型', '风险时间', '风险内容'],
          tableData: [],
        },
      ],
      qyfxData: [
        { name: '经营风险', value: 0 },
        { name: '安全生产风险', value: 0 },
        { name: '信用风险', value: 0 },
      ],
      jyfx: [
        { name: '严重违法企业', value: 0 },
        { name: '社保人数骤降', value: 0 },
        { name: '营收骤降', value: 0 },
        { name: '税收骤降', value: 0 },
        { name: '资产总计骤降', value: 0 },
        { name: '破产重整', value: 0 },
        { name: '裁判文书', value: 0 },
        { name: '行政处罚信息', value: 0 },
        { name: '清算信息', value: 0 },
        { name: '动产抵押', value: 0 },
        { name: '经营异常', value: 0 },
        { name: '股权冻结', value: 0 },
        { name: '整改整顿', value: 0 },
      ],
      aqsc: [
        { name: '环保违法处罚', value: 0 },
        { name: '重大火灾隐患', value: 0 },
        { name: '工矿领域安全事故', value: 0 },
      ],
      xyfx: [
        { name: '拖欠工资黑名单', value: 0 },
        { name: '失信', value: 0 },
        { name: '金融风险', value: 0 },
        { name: '行政强制', value: 0 },
        { name: '限制高消费', value: 0 },
      ],
      selectData: [
        {
          name: '基础标签',
          flag: false,
          value: '农、林、牧、渔业',
          strName: 'jcbq',
          children: [],
        },
        {
          name: '业务标签',
          flag: false,
          value: '五金',
          strName: 'ywbq',
          children: [],
        },
        {
          name: '个性标签',
          flag: false,
          value: '五金',
          strName: 'gxbq',
          children: [
            // '五金',
            // '纺织服装',
            // '智能光伏及新型储能',
            // '新能源汽车及关键零部件'
          ],
        },
      ],
    }
  },

  mounted() {
    this.getKeyName()
    this.initApi(0)
  },
  methods: {
    initApi(index) {
      let _this = this
      let that = this
      that.tabIndex = index
      _this.getData1()
      _this.getData2()

      // 企业风险-整顿整改的接口
      getCsdnInterface('qyhx_zgzd', { tyshxydm: this.allMessage[0].tyshxydm }).then((res) => {
        let nameArr = res.data.data.map((o) => o.zgzdlx)
        that.gsBq2 = Array.from(new Set(nameArr))
        that.getQyfxData(res.data)
      })

      getCsdnInterface('/csdn_qyhx_cyl').then((res) => {
        this.cylList = res.data
      })
      //知识产权-专利类型
      getCsdnInterface('/csdn_qyhx33', { ywwd2: this.allMessage[0].tyshxydm }).then((res) => {
        this.zscqList[0].tableData[0] = res.data.data.map((item) => {
          return {
            name: item.name,
            value: item.value,
          }
        })
      })
      //知识产权-专利法律状态
      getCsdnInterface('/csdn_qyhx34', { ywwd2: this.allMessage[0].tyshxydm }).then((res) => {
        this.zscqList[0].tableData[1] = res.data.data
      })
      //知识产权-商标国际分类发布
      getCsdnInterface('/csdn_qyhx35', { ywwd2: this.allMessage[0].tyshxydm }).then((res) => {
        this.zscqList[2].tableData[1] = res.data.cwData
      })
      //知识产权-商标申请状态发布
      getCsdnInterface('/csdn_qyhx36', { ywwd2: this.allMessage[0].tyshxydm }).then((res) => {
        this.zscqList[2].tableData[2] = res.data.data
      })

      // 企业标签分类
      let params = {
        name: '金华市城市大脑企业智服企业标签分类',
        url: 'http://dw.jinhua.gov.cn/gateway/api/001008007012243/dataSharing/4sP56fk07ddJpsh7.htm',
        // params: { tyshxydm: '91110101736457329F' },
        params: { tyshxydm: this.allMessage[0].tyshxydm },
      }
      getKfqIrsData(params).then((res) => {
        let data = res.data.data.result_data.datas.data
        if (data && data.length > 0) {
          data.forEach((el) => {
            if (el.bqfl == '基础标签') {
              this.selectData[0].children = el.bqmc.split(',')
            } else if (el.bqfl == '业务标签') {
              this.selectData[1].children = el.bqmc.split(',')
            } else if (el.bqfl == '个性标签') {
              this.selectData[2].children = el.bqmc.split(',')
            }
          })
        }
      })

      // 知识产权-专利列表
      let params2 = {
        name: '金华市城市大脑企业智服企业详情专利明细信息',
        url: 'http://dw.jinhua.gov.cn/gateway/api/001008007012243/dataSharing/Qbnhkr884eE43d0b.htm',
        params: { tyshxydm: this.allMessage[0].tyshxydm },
      }
      getKfqIrsData(params2).then((res) => {
        let data = res.data.data.result_data.datas.data.list
        if (data && data.length > 0) {
          this.zscqList.find((item) => item.title == '专利列表').tableData = data.map((item2) => {
            return {
              title: item2.title,
              pat_type: item2.patType,
              lrps: item2.lrps,
              app_number: item2.appNumber,
              app_date: item2.appDate,
              pub_number: item2.pubNumber,
              pub_date: item2.appDate,
              inventro_name: item2.inventroName,
            }
          })
        }
      })

      // 知识产权-商标列表
      let params3 = {
        name: '金华市城市大脑企业智服企业详情商标明细信息',
        url: 'http://dw.jinhua.gov.cn/gateway/api/001008007012243/dataSharing/L1eO8Pyf4atsmYfe.htm',
        params: { tyshxydm: this.allMessage[0].tyshxydm },
      }
      getKfqIrsData(params3).then((res) => {
        let data = res.data.data.result_data.datas.data.list
        if (data && data.length > 0) {
          this.zscqList.find((item) => item.title == '商标列表').tableData = data.list.map((item2) => {
            return {
              img: '-',
              name2: item2.tmName,
              name3: item2.intCls,
              name4: item2.useFlag,
              name5: item2.regNo,
              name6: item2.appDate,
              name7: item2.regDate,
            }
          })
        }
      })

      // 知识产权-著作权信息
      let params4 = {
        name: '金华市城市大脑企业智服企业详情软件著作权信息',
        url: 'http://dw.jinhua.gov.cn/gateway/api/001008007012243/dataSharing/e6EO0bd4c0KtsjS4.htm',
        params: { tyshxydm: this.allMessage[0].tyshxydm },
      }
      getKfqIrsData(params4).then((res) => {
        let data = res.data.data.result_data.datas.data
        if (data.list.length > 0) {
          this.zscqList
            .find((item) => item.title == '著作权')
            .tableTab.find((item2) => item2.name == '软件著作权').tableData = data.list.map((listItem) => {
            return {
              name1: listItem.rjqc,
              name2: listItem.rjjc,
              name3: listItem.bbh,
              name4: listItem.djh,
              name5: '-',
              name6: '-',
              name7: listItem.regDate,
              name8: '-',
            }
          })
        }
      })

      // 知识产权-著作权信息
      let params5 = {
        name: '金华市城市大脑企业智服企业司法信息',
        url: 'http://dw.jinhua.gov.cn/gateway/api/001008007012243/dataSharing/Lwwtv03bea4lc8D5.htm',
        params: { tyshxydm: this.allMessage[0].tyshxydm },
      }
      getKfqIrsData(params5).then((res) => {
        let data = res.data.data.result_data.datas.data.list
        if (data && data.length > 0) {
          this.sfajData = data
        }
      })

      // // 新-股东信息-股权穿透
      // let params6 = {
      //   name: '金华企业画像企业其他属性',
      //   url: 'http://dw.jinhua.gov.cn/gateway/api/001008007012229/dataSharing/d2Vsfghh18006364.htm',
      //   params: { tyshxydm: this.allMessage[0].tyshxydm },
      // }
      // getIrsData(params6).then((response) => {
      //   let res = response.data.datas.data
      //   let data = res[0].gdXx.sort(function (a, b) {
      //     return b.rjcze - a.rjcze
      //   })
      //   _this.listDataParent.parents = data.map((a) => {
      //     return {
      //       id: a.mcgdxx_tzr,
      //       name: a.mcgdxx_tzr,
      //       percent: a.czbl,
      //       ...a,
      //     }
      //   })
      // })

      // let params7 = {
      //   name: '企业穿透组件-母公司查子公司',
      //   url: 'https://bcdsg.zj.gov.cn:8443/restapi/prod/IC33000020220916000028/vspjk/vsp/front/enterprise',
      //   params: {
      //     type: 'fdcfxzf_zmgstzzb',
      //     qymc: `'${this.allMessage[0].qymc}'`, //"'赛丽地产集团有限公司'",
      //     appcode: '3e4fceacc88f4c129e3562ab12280e6d',
      //   },
      //   method: 'get',
      // }
      // getAssemblyData(params7).then((res) => {
      //   if (res.data.data) {
      //     let data = JSON.parse(res.data.data)
      //     _this.listDataChildren.children = data.map((a) => {
      //       return {
      //         id: a.zgsdm,
      //         name: a.zgsmc,
      //         percent: a.tzbl,
      //         ...a,
      //       }
      //     })
      //   }
      // })
    },

    // 切换
    getQyfxData(res) {
      let that = this
      that.jyfx.forEach((ele, i) => {
        ele.value = res.data.filter((a) => a.zgzdlx == ele.name).length
      })
      that.aqsc.forEach((ele, i) => {
        ele.value = res.data.filter((a) => a.zgzdlx == ele.name).length
      })
      that.xyfx.forEach((ele, i) => {
        ele.value = res.data.filter((a) => a.zgzdlx == ele.name).length
      })
      let yjfxSum = that.jyfx.filter((a) => a.value != 0).length
      let aqscSum = that.aqsc.filter((a) => a.value != 0).length
      let xyfxSum = that.xyfx.filter((a) => a.value != 0).length
      if (res.length != 0) {
        this.qyfxList[0].tableData = res.data.map((o) => {
          let str = { value1: o.zgzdlx, value2: o.yjsj, value3: o.yjnr }
          return str
        })

        const numOne = Math.round(100 - (yjfxSum / 13) * 100)
        this.qyfxData[0].value = numOne <= 60 ? '高' : numOne > 60 && numOne <= 80 ? '中' : '低'

        const numTwo = Math.round(100 - (aqscSum / 3) * 100)
        this.qyfxData[1].value = numTwo <= 60 ? '高' : numTwo > 60 && numTwo <= 80 ? '中' : '低'

        const numThree = Math.round(100 - (xyfxSum / 5) * 100)
        this.qyfxData[2].value = numThree <= 60 ? '高' : numThree > 60 && numThree <= 80 ? '中' : '低'
      }
    },
    openMag(item, obj) {},
    showMoreFun(ele) {
      let index = this.selectData.findIndex((o) => o.strName == ele.strName)
      this.selectData[index].flag = !this.selectData[index].flag
    },
    toBack() {
      this.sfajTitle = ['司法案件']
      this.showSfajDetail = false
    },
    toDetail(item) {
      this.sfajTitle = ['案件审理流程']
      this.showSfajDetail = true
      this.ajsllcData = item
    },
    changeTab(index) {
      this.secondIndex = index
      let time = this.cwData[1].tableData.map((item) => {
        return item.nbnd
      })
      let str = ['jlr', 'lrze', 'xs_yysr', 'nsze', 'zcze', 'fzze']
      let data = this.cwData[1].tableData.map((item) => {
        return item[str[index]]
      })
      this.$nextTick(() => {
        this.getCharts('qyhx-cwxx', time, data)
      })
    },

    // 拓扑图
    getData1() {
      let _this = this
      let params = {
        name: '金华企业画像企业基本属性',
        url: 'http://dw.jinhua.gov.cn/gateway/api/001008007012229/dataSharing/96802TFAe67RD677.htm',
        params: {
          tyshxydm: this.allMessage[0].tyshxydm,
        },
      }
      getIrsData(params).then((res) => {
        let code = res.data.code
        if (code != 0 || res.data.datas.data == undefined || res.data.datas.data.length == 0) {
          return
        }
        let data = res.data.datas.data
        _this.fr = data[0].fddbr
        _this.name = data[0].qymc
        _this.jbxxList = _this.fmObjToArr(data[0])
        _this.personName = data[0].fddbr
        // this.$EventBus.$emit('qyhxJbxxList', _this.jbxxList[0].value)
        this.$emit('qyhxJbxxList', _this.jbxxList[0].value)
      })
    },
    getData2() {
      let that = this
      // 金华91330701MA8G4H8B5N
      // 零跑

      let params = {
        name: '金华企业画像企业其他属性',
        url: 'http://dw.jinhua.gov.cn/gateway/api/001008007012229/dataSharing/d2Vsfghh18006364.htm',
        params: {
          tyshxydm: this.allMessage[0].tyshxydm,
        },
      }
      getIrsData(params).then((allData) => {
        let code = allData.data.code
        if (code != 0 || allData.data.datas.data == undefined || allData.data.datas.data.length == 0) {
          return
        }
        let res = allData.data.datas.data
        //标签信息
        if (res[0].zmbq) {
          that.gsBq = res[0].zmbq.split(',')
          // this.$EventBus.$emit('qyhxGsbq', that.gsBq)
          this.$emit('qyhxGsbq', that.gsBq)
        }

        // 财务信息
        that.cwData[1].tableData = res[0].cwXx
        if (res[0].cwXx.length > 0) {
          that.cwData[1].tableData = res[0].cwXx.map((ele) => {
            let str = {
              nbnd: ele.nbnd,
              fzze: ele.fzze,
              jlr: ele.jlr,
              lrze: ele.lrze,
              nsze: ele.nsze,
              qzzyywsr: ele.qzzyywsr,
              syzqyhj: ele.syzqyhj,
              xs_yysr: ele.xs_yysr,
              zcze: ele.zcze,
            }
            return str
          })
        }
        // 产业链
        if (res[0].cylXx) {
          that.cyl = res[0].cylXx[0].sscyl.split(',')
        }

        // 经营情况-财政补助企业兑现详情信息
        that.cwData[2].tableData = res[0].czbzqydxxqXx

        // 高新技术
        that.qygmxx[0].tableData = res[0].gxjsqyXx
        if (res[0].qygmXx.length > 0) {
          that.qygmxx[1].tableData = res[0].qygmXx.map((ele) => {
            let str = {
              sjqb: ele.sjqb,
              gsjfrs: ele.gsjfrs,
              syjfrs: ele.syjfrs,
              yljfrs: ele.yljfrs,
            }
            return str
          })
        }

        //企业变更
        that.qybgData = res[0].bgXx
        // 股东信息
        let gdxx = that.gdxxList.find((item) => item.title == '股东信息')
        if (gdxx) {
          that.gdxxList.find((item) => item.title == '股东信息').tableData = res[0].gdXx
        }

        // 企业公共信用评价信息
        that.xypjList[0].tableData = res[0].qyggxypjXx
        // 信用评价-金华市金阳光惠农惠企-企业信息
        that.xypjList[1].tableData = res[0].jyghnhqqyXx
        //信用等级-金华市区跪下工业企业亩产效益综合评价结果信息
        that.xypjList[2].tableData = res[0].jhsqgxgyqymcxyzhpjjgXx
        // 劳动保障诚信等级信息
        that.xypjList[3].tableData = res[0].ldbzcxdjXx
        // 企业环境行为信用评价信息
        that.xypjList[4].tableData = res[0].qyhjxwxypjXx

        // 拖欠工资黑名单信息
        that.fxyqList[0].tableData = res[0].tqgzhmdXx
        // 严重违法企业信息
        that.fxyqList[1].tableData = res[0].yzwfqyXx
        // 劳动保障处罚信息
        that.fxyqList[2].tableData = res[0].ldbzcfXx
        // 环境违法失信黑名单信息
        that.fxyqList[3].tableData = res[0].hjwfsxhmdXx
        // 风险舆情-企业经营异常名录信息
        that.fxyqList[4].tableData = res[0].qyjyycmlXx
        // 风险舆情-金融风险企业名单
        that.fxyqList[5].tableData = res[0].jrfxqymdXx
        // 风险舆情-失信信息
        that.fxyqList[6].tableData = res[0].sxXx
        // 风险舆情-行政处罚信息
        that.fxyqList[7].tableData = res[0].xzcfXx

        // 重大火灾隐患信息
        that.aqscList[0].tableData = res[0].zdhzyhXx
        // 危化品运输企业信息
        that.aqscList[1].tableData = res[0].whpysqyXx
        // 涉爆粉尘企业特性信息
        that.aqscList[2].tableData = res[0].sbfcqytxXx

        that.aqscList[3].tableData = res[0].xzqzXx
        // 危化品生产企业
        that.aqscList[4].tableData = res[0].whpscqyXx
        // 工矿商贸领域生产安全事故信息
        that.aqscList[5].tableData = res[0].gksmlyscaqsgXx
        // 行政强制信息
        that.aqscList[6].tableData = res[0].tzsbqyfxXx
        let fxData = res[0].gdXx

        let data = [
          {
            id: 1,
            pid: 0,
            name: '股东信息',
            alt: '',
            type: '人',
          },
        ]
        if (fxData.length != 0) {
          fxData.forEach((item) => {
            let id = Math.round(Math.random() * 495 + 5)
            let str = {
              id: id,
              pid: 1,
              type: '人',
              alt: item.czbl + '%',
              name: item.mcgdxx_tzr,
            }
            data.push(str)
          })
        }
        that.tptData = data
      })
    },
    changeItem(el) {
      if (el.title == '企业股权穿透') {
        this.constructorChart('appc1', this.listDataParent)
      } else if (el.title == '股东关联企业') {
        this.constructorChart('appc1', this.listDataParent)
      }
    },
    clickTab(index) {
      let that = this
      that.tabIndex = index
      that.activeName = 0
      let str = {
        1: 'cwData',
        3: 'xypjList',
        4: 'zscqList',
        5: 'qyfxList',
        6: 'sfajList',
        7: 'aqscList',
      }
      if (index != 0 && index != 2) {
        //index != 0 && index != 1 && index != 3
        this.allData = this[str[index]]
      }
      // if (that.tabIndex == 1) {
      //   that.$nextTick(() => {
      //     // that.getEcharts1('tyt1', this.name, that.tptData)  股东信息图表
      //     // that.getEcharts3('tyt1', that.tptData)
      //     that.listDataChildren.name = that.listDataParent.name = that.allMessage[0].qymc
      //     that.changeItem({ title: '企业股权穿透' })
      //     that.initMapCharts('echarts_dwtz1', this.gdxxList.find((item) => item.title == '对外投资分析').tableData[0])
      //     that.setdwtzEchrt3('echarts_dwtz2', this.gdxxList.find((item) => item.title == '对外投资分析').tableData[1])
      //     that.setdwtzEchrt1('echarts_dwtz3', this.gdxxList.find((item) => item.title == '对外投资分析').tableData[2])

      //     that.setzscqEchrt1(
      //       'echarts_dwtz4',
      //       this.gdxxList.find((item) => item.title == '对外投资分析').tableData[3],
      //       '状态分布'
      //     )
      //     that.setzscqEchrt1(
      //       'echarts_dwtz5',
      //       this.gdxxList.find((item) => item.title == '对外投资分析').tableData[4],
      //       '行业分布'
      //     )
      //     that.setdwtzEchrt2('echarts_dwtz6', this.gdxxList.find((item) => item.title == '对外投资分析').tableData[5])
      //   })
      // }
      if (that.tabIndex == 1) {
        that.$nextTick(() => {
          that.changeTab(0)
        })
      }
      if (that.tabIndex == 4) {
        that.$nextTick(() => {
          this.setzscqEchrt1('echarts_zscq1', this.zscqList[0].tableData[0], '专利类型', ' ')
          this.setzscqEchrt2('echarts_zscq2', this.zscqList[0].tableData[1])
          this.setzscqEchrt3('echarts_zscq3', this.zscqList[0].tableData[2])
          this.setzscqEchrt3('echarts_zscq4', this.zscqList[0].tableData[3])

          this.setzscqEchrt4('echarts_zscqSb1', this.zscqList[2].tableData[0])
          this.setzscqEchrt1('echarts_zscqSb2', this.zscqList[2].tableData[1], '商标国际\n\n分类分布', ' ')
          this.setzscqEchrt1('echarts_zscqSb3', this.zscqList[2].tableData[2], '商标申请\n\n状态分布', ' ')
        })
      }
      if (that.tabIndex == 5) {
        that.$nextTick(() => {
          this.getEchartRadar('echarts_fx1', this.jyfx, '204, 61, 64')
          this.getEchartRadar('echarts_fx2', this.aqsc, '216, 142, 64')
          this.getEchartRadar('echarts_fx3', this.xyfx, '111, 180, 70')
        })
      }
    },
    getKeyName() {
      let that = this
      getCsdnInterface2('qyhx/zdjx').then((res) => {
        that.keyName = res.data.data[0]
      })
    },
    openDialog(index) {
      // window.parent.lay.openIframe({
      //   type: 'openIframe',
      //   name: 'company-info',
      //   src: baseURL.url + '/static/citybrain/qyhx/commont/company-info.html',
      //   left: '2439px',
      //   top: '0px',
      //   width: '2800px',
      //   height: '1960px',
      //   zIndex: '9999',
      //   argument: {
      //     allMessage: this.allMessage,
      //     tabIndex: index,
      //     status: 'search',
      //   },
      // })
    },

    getCharts(id, time, data) {
      this.$echarts.init(document.getElementById(id)).dispose() // 销毁实例
      let myChart = this.$echarts.init(document.getElementById(id))
      let series = []
      if (data.length > 1) {
        series = [
          {
            type: 'line',
            symbol: 'circle',
            smooth: true,
            symbolSize: 15,
            itemStyle: {
              normal: {
                color: '#0092f6',
                lineStyle: {
                  color: '#0092f6',
                  width: 2,
                },
              },
            },
            data: data.reverse(),
          },
        ]
      } else {
        series = [
          {
            type: 'bar',
            barWidth: '5%',
            itemStyle: {
              normal: {
                color: '#0092f6',
              },
            },
            data: data.reverse(),
          },
        ]
      }
      let option = {
        grid: {
          left: '5%',
          right: '3%',
          top: '20%',
          bottom: '7%',
          containLabel: true,
        },
        tooltip: {
          show: true,
          trigger: 'item',
          confine: true,
          textStyle: {
            color: 'rgba(212, 232, 254, 1)',
            fontSize: 28,
          },
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          axisPointer: {
            lineStyle: {
              color: 'rgba(11, 208, 241, 1)',
              type: 'slider',
            },
          },
        },
        xAxis: [
          {
            type: 'category',
            // boundaryGap: false,
            axisLabel: {
              color: '#fff',
              fontSize: 30,
              interval: 0,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#fff',
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: '#fff',
              },
            },
            data: time.reverse(),
          },
        ],
        yAxis: [
          {
            type: 'value',
            name: '单位:万元',
            nameTextStyle: {
              color: '#fff',
              fontSize: 24,
              padding: [0, 20, 10, 0],
            },
            axisLabel: {
              textStyle: {
                color: '#fff',
                fontSize: 30,
              },
            },
            axisLine: {
              lineStyle: {
                color: 'rgba(0, 192, 255, 0.2)',
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(0, 192, 255, 0.2)',
              },
            },
          },
        ],
        series: series,
      }
      myChart.setOption(option, true)
    },
    // 企业风险雷达图
    getEchartRadar(id, echartsData, rgbaColor) {
      this.$echarts.init(document.getElementById(id)).dispose()
      let myChart = this.$echarts.init(document.getElementById(id))
      let max = Math.max.apply(
        null,
        echartsData.map((a) => a.value)
      )
      const indicator = echartsData.map((a, i) => {
        return {
          name: a.name,
          max: max == 0 ? 10 : max * 1.2,
        }
      })
      let option = {
        tooltip: {
          show: false,
          textStyle: {
            color: 'rgba(212, 232, 254, 1)',
            fontSize: 28,
          },
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
        },
        color: ['#068AC3', '#B2782C'],
        legend: { show: false },
        radar: {
          center: ['50%', '55%'], // 外圆的位置
          radius: '75%',
          name: {
            rich: {
              a: {
                fontSize: 28,
                color: '#ccc',
                lineHeight: '35',
              },
              b: {
                color: '#FFF',
                fontSize: 28,
                padding: [0, 0, 0, 15],
              },
            },
            formatter: (per) => {
              let obj = echartsData.find((a) => a.name == per).value
              return `{a|${per}}\n{b|${obj}}`
            },
            textStyle: {
              color: '#fff',
              fontSize: 28,
              fontWeight: 400,
              fontFamily: 'PingFangSC-Regular,PingFang SC',
              fontStyle: 'italic',
            },
          },
          indicator: indicator,
          splitArea: {
            show: true,
            areaStyle: {
              color: ['transparent'], // 画布颜色 // 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。
            },
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.2)',
            },
          },
          splitLine: {
            lineStyle: {
              type: 'solid',
              color: ['#1781BA', '#1781BA'], // 分隔线颜色
              width: 1, // 分隔线线宽
            },
          },
        },
        series: [
          {
            type: 'radar',
            symbolSize: 5,
            data: [
              {
                value: echartsData.map((a) => a.value),
                areaStyle: {
                  normal: {
                    color: {
                      type: 'radial',
                      x: 0.5,
                      y: 0.5,
                      r: 0.5,
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(' + rgbaColor + ', 0.14)',
                        },

                        {
                          offset: 1,
                          color: 'rgba(' + rgbaColor + ', 1)', // 100% 处的颜色
                        },
                      ],
                      global: false, // 缺省为 false
                    },
                  },
                },
                itemStyle: {
                  normal: {
                    lineStyle: {
                      width: 1,
                    },
                    opacity: 0.3,
                  },
                  emphasis: {
                    lineStyle: {
                      width: 5,
                    },
                    opacity: 0,
                  },
                },
              },
            ],
          },
        ],
      }
      myChart.setOption(option, true)
    },
    // 知识产权饼图
    setzscqEchrt1(id, echartData, tit, unit) {
      let myEc = this.$echarts.init(document.getElementById(id))
      const colorList = [
        '#0091ff ',
        '#13b28e',
        '#e77930',
        '#fadc6d',
        '#0061ff',
        '#f7ba1e',
        '#d53939',
        '#e68079',
        '#f1af76',
      ]
      let option = {
        title: {
          text: tit,
          textStyle: {
            fontSize: 36,
            color: '#fff',
          },
          subtextStyle: {
            fontSize: 35,
            color: '#a0e2fe',
          },
          textAlign: 'center',
          left: '17%',
          top: 'center',
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: '#000000',
          textStyle: {
            color: 'white',
            fontSize: '27',
          },
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          left: '40%',
          top: 'center',
          itemGap: 30,
          selectedMode: false,
          icon: 'pin',
          textStyle: {
            color: '#fff',
            rich: {
              uname: {
                width: 200,
                fontSize: 29,
              },
              unum: {
                color: '#fff',
                fontSize: 32,
                fontWeight: 700,
                align: 'right',
              },
            },
          },
          formatter(name) {
            return `{uname|${name}}{unum|${echartData.filter((a) => a.name == name)[0].value}${unit || '%'}}`
          },
        },
        color: colorList,
        series: [
          {
            type: 'pie',
            radius: [160, 190],
            center: ['17%', '50%'],
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            data: echartData,
          },
        ],
      }
      myEc.setOption(option)
      myEc.getZr().on('mousemove', (param) => {
        myEc.getZr().setCursorStyle('default')
      })
    },
    setzscqEchrt2(id, echartData) {
      this.$echarts.init(document.getElementById(id)).dispose()
      let myEc = this.$echarts.init(document.getElementById(id))
      let option = {
        title: {
          show: echartData.length == 0,
          text: echartData.length == 0 && '暂无数据',
          textStyle: {
            fontSize: 36,
            color: '#fff',
          },
          subtextStyle: {
            fontSize: 35,
            color: '#a0e2fe',
          },
          textAlign: 'center',
          left: 'center',
          top: 'center',
        },
        grid: {
          bottom: 15,
          left: '2%',
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
          textStyle: {
            color: 'white',
            fontSize: '30',
          },
          formatter: '{b}: {c}',
        },
        barWidth: 25,
        xAxis: {
          type: 'value',
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: 'rgba(250,250,250,0.6)',
              fontSize: 28,
            },
          },
        },
        yAxis: {
          type: 'category',
          data: echartData.map((a) => a.name),
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {},
          axisLabel: {
            textStyle: {
              color: 'rgba(250,250,250,0.6)',
              fontSize: 30,
            },
          },
        },
        series: [
          {
            type: 'bar',
            itemStyle: {
              normal: {
                label: {
                  show: false, //开启显示
                  position: 'right', //在上方显示
                  textStyle: {
                    //数值样式
                    color: 'rgba(250,250,250,0.6)',
                    fontSize: 16,
                    fontWeight: 600,
                  },
                },
                color: new this.$echarts.graphic.LinearGradient(1, 0, 0, 0, [
                  {
                    offset: 0,
                    color: 'rgba(61,126,235,1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(61,126,235,0)',
                  },
                ]),
                borderWidth: 2,
                barBorderRadius: 15,
              },
            },
            data: echartData.map((a) => a.value),
          },
        ],
      }
      myEc.setOption(option)
      myEc.getZr().on('mousemove', (param) => {
        myEc.getZr().setCursorStyle('default')
      })
    },
    setzscqEchrt3(id, echartData, col) {
      this.$echarts.init(document.getElementById(id)).dispose()
      let myEc = this.$echarts.init(document.getElementById(id))
      const colorList = ['#0091ff ', '#13b38f', '#e77930', '#fadc6d']
      let option = {
        legend: {
          icon: 'circle',
          data: echartData.map((a) => a.name),
          top: '10%',
          itemGap: 35,
          itemWidth: 20,
          itemHeight: 20,
          textStyle: {
            color: '#FFF',
            fontSize: 28,
          },
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
          axisPointer: {
            type: 'line', // 默认为直线，可选为：'line' | 'shadow'
          },
          textStyle: {
            color: 'white',
            fontSize: '30',
          },
        },
        grid: {
          left: '5%',
          right: '6%',
          top: '20%',
          bottom: '10%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: echartData[0].list.map((a) => a.time),
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              // rotate: '20',
              interval: 0,
              textStyle: {
                color: '#D6E7F9',
                fontSize: 28,
              },
            },
          },
        ],
        yAxis: [
          {
            name: '单位：件',
            type: 'value',
            nameTextStyle: {
              fontSize: 32,
              color: '#D6E7F9',
              padding: [0, 50, 20, 0],
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: '#D6E7F9',
              },
            },
          },
        ],
        color: colorList,
        series: echartData.map((a, i) => {
          return {
            name: a.name,
            type: 'line',
            lineStyle: {
              normal: {
                width: 3,
              },
            },
            symbol: 'emptyCircle',
            symbolSize: 10,
            color: colorList[i],
            data: a.list.map((s) => s.value),
          }
        }),
      }
      myEc.setOption(option)
      myEc.getZr().on('mousemove', (param) => {
        myEc.getZr().setCursorStyle('default')
      })
    },
    setzscqEchrt4(id, echartData) {
      this.$echarts.init(document.getElementById(id)).dispose()
      let myEc = this.$echarts.init(document.getElementById(id))
      let option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
          axisPointer: {
            type: 'line', // 默认为直线，可选为：'line' | 'shadow'
          },
          textStyle: {
            color: 'white',
            fontSize: '30',
          },
        },
        grid: {
          left: '5%',
          right: '6%',
          top: '20%',
          bottom: '10%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: echartData.map((a) => a.time),
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              // rotate: '20',
              interval: 0,
              textStyle: {
                color: '#D6E7F9',
                fontSize: 28,
              },
            },
          },
        ],
        yAxis: [
          {
            name: '单位：个',
            type: 'value',
            nameTextStyle: {
              fontSize: 32,
              color: '#D6E7F9',
              padding: [0, 50, 20, 0],
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: '#D6E7F9',
              },
            },
          },
        ],
        series: [
          {
            type: 'line',
            lineStyle: {
              normal: {
                width: 3,
              },
            },
            symbol: 'emptyCircle',
            symbolSize: 10,
            color: 'rgba(0,192,255)',
            areaStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(0,192,255,0.3)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(0,192,255,0)',
                    },
                  ],
                  false
                ),
                shadowColor: 'rgba(0,192,255,0.9)',
                shadowBlur: 20,
              },
            },
            data: echartData.map((s) => s.value),
          },
        ],
      }
      myEc.setOption(option)
      myEc.getZr().on('mousemove', (param) => {
        myEc.getZr().setCursorStyle('default')
      })
    },

    initMapCharts(id, echartData) {
      const that = this
      this.$echarts.init(document.getElementById(id)).dispose()
      axios.get('../json/china.json').then((res) => {
        // console.log(res.data)
        this.$echarts.registerMap('china', res.data)
        let myEc = this.$echarts.init(document.getElementById(id))
        let option = {
          tooltip: {
            trigger: 'item',
            backgroundColor: 'rgba(51, 51, 51, 0.7)',
            borderWidth: 0,
            textStyle: {
              color: 'white',
              fontSize: '30',
            },
            formatter: '{b}: {c}',
          },
          legend: {
            show: false,
          },
          grid: {
            // 仅仅控制条形图的位置
            show: false,
            containLabel: true,
            right: 120,
            top: 50,
            bottom: 30,
            width: '30%',
          },
          visualMap: {
            type: 'continuous',
            min: 0,
            max: 1000,
            text: ['多', '少'],
            seriesIndex: 0,
            dimension: 0,
            realtime: false,
            left: 0,
            itemWidth: 18,
            itemHeight: 100,
            calculable: true,
            inRange: {
              color: ['rgba(159,205,253,0.50)', '#60ACFC'],
              symbolSize: [100, 100],
            },
            outOfRange: {
              color: ['#eeeeee'],
              symbolSize: [100, 100],
            },
          },
          toolbox: {
            show: false,
          },

          xAxis: [
            {
              type: 'value',
              position: 'top',
              inside: false,
              axisLabel: {
                show: false,
              },
              splitLine: {
                show: false,
              },
              margin: 10,
            },
          ],
          yAxis: [
            {
              type: 'category',
              boundaryGap: true,
              splitLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              axisLine: {},
              axisLabel: {
                textStyle: {
                  color: 'rgba(250,250,250,0.6)',
                  fontSize: 30,
                },
              },
              data: that.getTopTen(echartData).map((item) => item.name),
            },
          ],

          series: [
            {
              type: 'map',
              mapType: 'china',
              left: '100',
              top: '20',
              width: '40%',
              roam: 'move',
              mapValueCalculation: 'sum',
              zoom: 1,
              label: {
                normal: {
                  textStyle: {
                    color: '#666',
                  },
                },
                emphasis: {
                  textStyle: {
                    color: '#234EA5',
                  },
                },
              },
              itemStyle: {
                normal: {
                  areaColor: '#EEEEEE',
                  borderColor: '#FFFFFF',
                },
                emphasis: {
                  areaColor: '#E5F39B',
                },
              },
              data: echartData,
            },
            {
              type: 'bar',
              roam: false,
              visualMap: false,
              barWidth: 25,
              itemStyle: {
                normal: {
                  label: {
                    show: false, //开启显示
                    position: 'right', //在上方显示
                    textStyle: {
                      //数值样式
                      color: 'rgba(250,250,250,0.6)',
                      fontSize: 16,
                      fontWeight: 600,
                    },
                  },
                  color: new this.$echarts.graphic.LinearGradient(1, 0, 0, 0, [
                    {
                      offset: 0,
                      color: 'rgba(61,126,235,1)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(61,126,235,0)',
                    },
                  ]),
                  borderWidth: 2,
                  barBorderRadius: 15,
                },
              },
              label: {
                normal: {
                  show: true,
                  position: 'right',
                  textStyle: {
                    //数值样式
                    color: 'rgba(250,250,250,0.6)',
                    fontSize: 28,
                    fontWeight: 600,
                  },
                  formatter: function (params) {
                    return params.value + '家'
                  },
                },
                emphasis: {
                  show: true,
                },
              },
              data: that.getTopTen(echartData),
            },
          ],
        }
        myEc.setOption(option)
        myEc.getZr().on('mousemove', (param) => {
          myEc.getZr().setCursorStyle('default')
        })
      })
    },
    setdwtzEchrt1(id, echartData) {
      this.$echarts.init(document.getElementById(id)).dispose()
      let myEc = this.$echarts.init(document.getElementById(id))
      let option = {
        grid: {
          left: '5%',
          right: '6%',
          top: '20%',
          bottom: '10%',
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
          textStyle: {
            color: 'white',
            fontSize: '30',
          },
          formatter: '{b}: {c}',
        },
        barWidth: 25,
        xAxis: {
          type: 'category',
          data: echartData.map((a) => a.name),
          splitLine: {
            show: false,
          },
          axisLine: {
            lineStyle: {
              color: 'rgb(119,179,241,.4)', // 颜色
              width: 1, // 粗细
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            // rotate: '20',
            interval: 0,
            textStyle: {
              color: '#D6E7F9',
              fontSize: 28,
            },
          },
        },
        yAxis: {
          name: '家',
          type: 'value',
          nameTextStyle: {
            fontSize: 32,
            color: '#D6E7F9',
            padding: [0, 50, 20, 0],
          },
          splitLine: {
            lineStyle: {
              color: 'rgb(119,179,241,.4)',
            },
          },
          axisLabel: {
            textStyle: {
              fontSize: 28,
              color: '#D6E7F9',
            },
          },
        },
        series: [
          {
            type: 'bar',
            itemStyle: {
              normal: {
                label: {
                  show: false, //开启显示
                  position: 'right', //在上方显示
                  textStyle: {
                    //数值样式
                    color: 'rgba(250,250,250,0.6)',
                    fontSize: 16,
                    fontWeight: 600,
                  },
                },
                color: 'rgba(61,126,235,1)',
                borderWidth: 2,
                barBorderRadius: 15,
              },
            },
            data: echartData.map((a) => a.value),
          },
        ],
      }
      myEc.setOption(option)
      myEc.getZr().on('mousemove', (param) => {
        myEc.getZr().setCursorStyle('default')
      })
    },
    setdwtzEchrt2(id, echartData) {
      this.$echarts.init(document.getElementById(id)).dispose()
      let myEc = this.$echarts.init(document.getElementById(id))
      let option = {
        grid: {
          bottom: 15,
          left: '2%',
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
          textStyle: {
            color: 'white',
            fontSize: '30',
          },
          formatter: '{b}: {c}',
        },
        barWidth: 25,
        xAxis: {
          name: '家',
          type: 'value',
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: 'rgba(250,250,250,0.6)',
              fontSize: 28,
            },
          },
        },
        yAxis: {
          type: 'category',
          data: echartData.map((a) => a.name),
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {},
          axisLabel: {
            textStyle: {
              color: 'rgba(250,250,250,0.6)',
              fontSize: 30,
            },
          },
        },
        series: [
          {
            type: 'bar',
            itemStyle: {
              normal: {
                label: {
                  show: false, //开启显示
                  position: 'right', //在上方显示
                  textStyle: {
                    //数值样式
                    color: 'rgba(250,250,250,0.6)',
                    fontSize: 16,
                    fontWeight: 600,
                  },
                },
                color: new this.$echarts.graphic.LinearGradient(1, 0, 0, 0, [
                  {
                    offset: 0,
                    color: 'rgba(61,126,235,1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(61,126,235,0)',
                  },
                ]),
                borderWidth: 2,
                barBorderRadius: 15,
              },
            },
            data: echartData.map((a) => a.value),
          },
        ],
      }
      myEc.setOption(option)
      myEc.getZr().on('mousemove', (param) => {
        myEc.getZr().setCursorStyle('default')
      })
    },
    setdwtzEchrt3(id, echartData, col) {
      this.$echarts.init(document.getElementById(id)).dispose()
      let myEc = this.$echarts.init(document.getElementById(id))
      const colorList = ['#0091ff ', '#13b38f', '#e77930', '#fadc6d']
      let option = {
        legend: {
          icon: 'circle',
          data: echartData.map((a) => a.name),
          top: '10%',
          itemGap: 35,
          itemWidth: 20,
          itemHeight: 20,
          textStyle: {
            color: '#FFF',
            fontSize: 28,
          },
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
          axisPointer: {
            type: 'line', // 默认为直线，可选为：'line' | 'shadow'
          },
          textStyle: {
            color: 'white',
            fontSize: '30',
          },
        },
        grid: {
          left: '5%',
          right: '6%',
          top: '20%',
          bottom: '10%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: echartData[0].list.map((a) => a.time),
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              // rotate: '20',
              interval: 0,
              textStyle: {
                color: '#D6E7F9',
                fontSize: 28,
              },
            },
          },
        ],
        yAxis: [
          {
            name: '单位：家',
            type: 'value',
            nameTextStyle: {
              fontSize: 32,
              color: '#D6E7F9',
              padding: [0, 50, 20, 0],
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: '#D6E7F9',
              },
            },
          },
        ],
        color: colorList,
        series: echartData.map((a, i) => {
          return {
            name: a.name,
            type: 'line',
            lineStyle: {
              normal: {
                width: 3,
              },
            },
            symbol: 'emptyCircle',
            symbolSize: 10,
            color: colorList[i],
            data: a.list.map((s) => s.value),
          }
        }),
      }
      myEc.setOption(option)
      myEc.getZr().on('mousemove', (param) => {
        myEc.getZr().setCursorStyle('default')
      })
    },
    //返回数组中value值最大的十项组成的新数组
    getTopTen(arr) {
      arr.sort((a, b) => b.value - a.value)
      // 只保留前十项
      const maxTen = arr.slice(0, 10)
      return maxTen
    },

    // 股权树
    constructorChart(id, echartData) {
      this.d3.select('svg').remove()
      // 树的源数据
      this.originTreeData = echartData
      // 宿主元素选择器
      this.el = document.getElementById(id)
      // 一些配置项
      this.config = {
        // 节点的横向距离
        dx: 250,
        // 节点的纵向距离
        dy: 170,
        // svg的viewBox的宽度
        width: 0,
        // svg的viewBox的高度
        height: 800,
        // 节点的矩形框宽度
        rectWidth: 170,
        // 节点的矩形框高度
        rectHeight: 70,
      }
      this.svg = null
      this.gAll = null
      this.gLinks = null
      this.gNodes = null
      // 给树加坐标点的方法
      this.tree = null
      // 投资公司树的根节点
      this.rootOfDown = null
      // 股东树的根节点
      this.rootOfUp = null
      this.drawChart({
        type: 'fold',
      })
    },
    // 初始化树结构数据
    drawChart(options) {
      // 宿主元素的d3选择器对象
      let host = this.d3.select(this.el)
      // 宿主元素的DOM，通过node()获取到其DOM元素对象
      let dom = host.node()
      // 宿主元素的DOMRect
      let domRect = dom.getBoundingClientRect()
      // svg的宽度和高度
      this.config.width = domRect.width
      this.config.height = domRect.height
      let oldSvg = this.d3.select('svg')
      // 如果宿主元素中包含svg标签了，那么则删除这个标签，再重新生成一个
      if (!oldSvg.empty()) {
        oldSvg.remove()
      }
      const svg = this.d3
        .create('svg')
        .attr('viewBox', () => {
          let parentsLength = this.originTreeData.parents ? this.originTreeData.parents.length : 0
          return [
            -this.config.width / 2,
            // 如果有父节点，则根节点居中，否则根节点上浮一段距离
            parentsLength > 0 ? -this.config.height / 2 : -this.config.height / 3,
            this.config.width,
            this.config.height,
          ]
        })
        .style('user-select', 'none')
        .style('cursor', 'move')

      // 包括连接线和节点的总集合
      const gAll = svg.append('g').attr('id', 'all')
      svg
        .call(
          this.d3
            .zoom()
            .scaleExtent([0.2, 5])
            .on('zoom', (e) => {
              gAll.attr('transform', () => {
                return `translate(${e.transform.x},${e.transform.y}) scale(${e.transform.k})`
              })
            })
        )
        .on('dblclick.zoom', null) // 取消默认的双击放大事件
      this.gAll = gAll
      // 连接线集合
      this.gLinks = gAll.append('g').attr('id', 'linkGroup')
      // 节点集合
      this.gNodes = gAll.append('g').attr('id', 'nodeGroup')
      // 设置好节点之间距离的tree方法
      this.tree = this.d3.tree().nodeSize([this.config.dx, this.config.dy])
      this.rootOfDown = this.d3.hierarchy(this.originTreeData, (d) => d.children)
      this.rootOfUp = this.d3.hierarchy(this.originTreeData, (d) => d.parents)
      this.tree(this.rootOfDown)
      ;[this.rootOfDown.descendants(), this.rootOfUp.descendants()].forEach((nodes) => {
        nodes.forEach((node) => {
          node._children = node.children || null
          if (options.type === 'all') {
            //如果是all的话，则表示全部都展开
            node.children = node._children
          } else if (options.type === 'fold') {
            //如果是fold则表示除了父节点全都折叠
            // 将非根节点的节点都隐藏掉（其实对于这个组件来说加不加都一样）
            if (node.depth) {
              node.children = null
            }
          }
        })
      })
      //箭头(下半部分)
      svg
        .append('marker')
        .attr('id', 'markerOfDown')
        .attr('markerUnits', 'userSpaceOnUse')
        .attr('viewBox', '0 -5 10 10') //坐标系的区域
        .attr('refX', 55) //箭头坐标
        .attr('refY', 0)
        .attr('markerWidth', 10) //标识的大小
        .attr('markerHeight', 10)
        .attr('orient', '90') //绘制方向，可设定为：auto（自动确认方向）和 角度值
        .attr('stroke-width', 2) //箭头宽度
        .append('path')
        .attr('d', 'M0,-5L10,0L0,5') //箭头的路径
        .attr('fill', '#215af3') //箭头颜色
      //箭头(上半部分)
      svg
        .append('marker')
        .attr('id', 'markerOfUp')
        .attr('markerUnits', 'userSpaceOnUse')
        .attr('viewBox', '0 -5 10 10') //坐标系的区域
        .attr('refX', -50) //箭头坐标
        .attr('refY', 0)
        .attr('markerWidth', 10) //标识的大小
        .attr('markerHeight', 10)
        .attr('orient', '90') //绘制方向，可设定为：auto（自动确认方向）和 角度值
        .attr('stroke-width', 2) //箭头宽度
        .append('path')
        .attr('d', 'M0,-5L10,0L0,5') //箭头的路径
        .attr('fill', '#215af3') //箭头颜色
      this.svg = svg
      this.update()
      // 将svg置入宿主元素中
      host.append(function () {
        return svg.node()
      })
    },
    // 更新数据
    update(source) {
      if (!source) {
        source = {
          x0: 0,
          y0: 0,
        }
        // 设置根节点所在的位置（原点）
        this.rootOfDown.x0 = 0
        this.rootOfDown.y0 = 0
        this.rootOfUp.x0 = 0
        this.rootOfUp.y0 = 0
      }
      let nodesOfDown = this.rootOfDown.descendants().reverse()
      let linksOfDown = this.rootOfDown.links()
      let nodesOfUp = this.rootOfUp.descendants().reverse()
      let linksOfUp = this.rootOfUp.links()
      this.tree(this.rootOfDown)
      this.tree(this.rootOfUp)
      const myTransition = this.svg.transition().duration(500)
      /***  绘制子公司树  ***/
      const node1 = this.gNodes.selectAll('g.nodeOfDownItemGroup').data(nodesOfDown, (d) => {
        return d.data.id
      })

      const node1Enter = node1
        .enter()
        .append('g')
        .attr('class', 'nodeOfDownItemGroup')
        .attr('transform', (d) => {
          return `translate(${source.x0},${source.y0})`
        })
        .attr('fill-opacity', 0)
        .attr('stroke-opacity', 0)
        .style('cursor', 'pointer')

      // 外层的矩形框
      node1Enter
        .append('rect')
        .attr('width', (d) => {
          if (d.depth === 0) {
            return (d.data.name.length + 2) * 16
          }
          return this.config.rectWidth
        })
        .attr('height', (d) => {
          if (d.depth === 0) {
            return 30
          }
          return this.config.rectHeight
        })
        .attr('x', (d) => {
          if (d.depth === 0) {
            return (-(d.data.name.length + 2) * 16) / 2
          }
          return -this.config.rectWidth / 2
        })
        .attr('y', (d) => {
          if (d.depth === 0) {
            return -15
          }
          return -this.config.rectHeight / 2
        })
        .attr('rx', 5)
        .attr('stroke-width', 1)
        .attr('stroke', (d) => {
          if (d.depth === 0) {
            return '#5682ec'
          }
          return '#7A9EFF'
        })
        .attr('fill', (d) => {
          if (d.depth === 0) {
            return '#7A9EFF'
          }
          return '#20C0F4'
        })
        .on('click', (e, d) => {
          this.nodeClickEvent(e, d)
        })
      // 文本主标题
      node1Enter
        .append('text')
        .attr('class', 'main-title')
        .attr('x', (d) => {
          return 0
        })
        .attr('y', (d) => {
          if (d.depth === 0) {
            return 5
          }
          return -14
        })
        .attr('text-anchor', (d) => {
          return 'middle'
        })
        .text((d) => {
          if (d.depth === 0) {
            return d.data.name
          } else {
            return d.data.name.length > 11 ? d.data.name.substring(0, 11) : d.data.name
          }
        })
        .attr('fill', (d) => {
          if (d.depth === 0) {
            return '#FFFFFF'
          }
          return '#ffffff'
        })
        .style('font-size', (d) => (d.depth === 0 ? 16 : 14))
        .style('font-family', '黑体')
        .style('font-weight', 'bold')
      // 副标题
      node1Enter
        .append('text')
        .attr('class', 'sub-title')
        .attr('x', (d) => {
          return 0
        })
        .attr('y', (d) => {
          return 5
        })
        .attr('text-anchor', (d) => {
          return 'middle'
        })
        .text((d) => {
          if (d.depth !== 0) {
            let subTitle = d.data.name.substring(11)
            if (subTitle.length > 10) {
              return subTitle.substring(0, 10) + '...'
            }
            return subTitle
          }
        })
        .attr('fill', '#FFFFFF')
        .style('font-size', (d) => 14)
        .style('font-family', '黑体')
        .style('font-weight', 'bold')

      // 认缴金额
      // node1Enter.append('text')
      //   .attr("class", "sub-title")
      //   .attr('x', 0)
      //   .attr('y', 25)
      //   // .attr('dy', d => d.data.name.substr(10, d.data.name.length).length ? '4.5em' : '4.1em')
      //   .attr('text-anchor', 'middle')
      //   .attr('fill', d => d.depth ? '#445166' : '#fff')
      //   .text(d => d.data.money ? d.data.money.length > 12 ? `认缴金额：${d.data.money.substr(0, 10)}…` : `认缴金额：${d.data.money}万元` : '')
      //   .style('font-size', '12px')
      //   .style('font-family', '黑体')
      //   .style('font-weight', 'bold')
      //   .style('color', '#000000');
      // 控股比例
      node1Enter
        .append('text')
        .attr('class', 'percent')
        .attr('x', (d) => {
          return 12
        })
        .attr('y', (d) => {
          return -45
        })
        .text((d) => {
          if (d.depth !== 0) {
            return d.data.percent
          }
        })
        .attr('fill', '#FFFFFF')
        .style('font-family', '黑体')
        .style('font-size', (d) => 14)

      // 增加展开按钮
      const expandBtnG = node1Enter
        .append('g')
        .attr('class', 'expandBtn')
        .attr('transform', (d) => {
          return `translate(${0},${this.config.rectHeight / 2})`
        })
        .style('display', (d) => {
          // 如果是根节点，不显示
          if (d.depth === 0) {
            return 'none'
          }
          // 如果没有子节点，则不显示
          if (!d._children) {
            return 'none'
          }
        })
        .on('click', (e, d) => {
          if (d.children) {
            d._children = d.children
            d.children = null
          } else {
            d.children = d._children
          }
          this.update(d)
        })

      expandBtnG.append('circle').attr('r', 8).attr('fill', '#7A9EFF').attr('cy', 8)

      expandBtnG
        .append('text')
        .attr('text-anchor', 'middle')
        .attr('fill', '#ffffff')
        .attr('y', 13)
        .style('font-size', 16)
        .style('font-family', '微软雅黑')
        .text((d) => {
          return d.children ? '-' : '+'
        })

      const link1 = this.gLinks.selectAll('path.linkOfDownItem').data(linksOfDown, (d) => d.target.data.id)

      const link1Enter = link1
        .enter()
        .append('path')
        .attr('class', 'linkOfDownItem')
        .attr('d', (d) => {
          let o = {
            source: {
              x: source.x0,
              y: source.y0,
            },
            target: {
              x: source.x0,
              y: source.y0,
            },
          }
          return this.drawLink(o)
        })
        .attr('fill', 'none')
        .attr('stroke', '#7A9EFF')
        .attr('stroke-width', 1)
        .attr('marker-end', 'url(#markerOfDown)')

      // 有元素update更新和元素新增enter的时候
      node1
        .merge(node1Enter)
        .transition(myTransition)
        .attr('transform', (d) => {
          return `translate(${d.x},${d.y})`
        })
        .attr('fill-opacity', 1)
        .attr('stroke-opacity', 1)

      // 有元素消失时
      node1
        .exit()
        .transition(myTransition)
        .remove()
        .attr('transform', (d) => {
          return `translate(${source.x0},${source.y0})`
        })
        .attr('fill-opacity', 0)
        .attr('stroke-opacity', 0)

      link1.merge(link1Enter).transition(myTransition).attr('d', this.drawLink)

      link1
        .exit()
        .transition(myTransition)
        .remove()
        .attr('d', (d) => {
          let o = {
            source: {
              x: source.x,
              y: source.y,
            },
            target: {
              x: source.x,
              y: source.y,
            },
          }
          return this.drawLink(o)
        })

      /***  绘制股东树  ***/

      nodesOfUp.forEach((node) => {
        node.y = -node.y
      })

      const node2 = this.gNodes.selectAll('g.nodeOfUpItemGroup').data(nodesOfUp, (d) => {
        return d.data.id
      })

      const node2Enter = node2
        .enter()
        .append('g')
        .attr('class', 'nodeOfUpItemGroup')
        .attr('transform', (d) => {
          return `translate(${source.x0},${source.y0})`
        })
        .attr('fill-opacity', 0)
        .attr('stroke-opacity', 0)
        .style('cursor', 'pointer')

      // 外层的矩形框
      node2Enter
        .append('rect')
        .attr('width', (d) => {
          if (d.depth === 0) {
            return (d.data.name.length + 2) * 16
          }
          return this.config.rectWidth
        })
        .attr('height', (d) => {
          if (d.depth === 0) {
            return 30
          }
          return this.config.rectHeight
        })
        .attr('x', (d) => {
          if (d.depth === 0) {
            return (-(d.data.name.length + 2) * 16) / 2
          }
          return -this.config.rectWidth / 2
        })
        .attr('y', (d) => {
          if (d.depth === 0) {
            return -15
          }
          return -this.config.rectHeight / 2
        })
        .attr('rx', 5)
        .attr('stroke-width', 1)
        .attr('stroke', (d) => {
          if (d.depth === 0) {
            return '#5682ec'
          }
          return '#7A9EFF'
        })
        .attr('fill', (d) => {
          if (d.depth === 0) {
            return '#7A9EFF'
          }
          return '#20C0F4'
        })
        .on('click', (e, d) => {
          this.nodeClickEvent(e, d)
        })
      // 文本主标题
      node2Enter
        .append('text')
        .attr('class', 'main-title')
        .attr('x', (d) => {
          return 0
        })
        .attr('y', (d) => {
          if (d.depth === 0) {
            return 5
          }
          return -14
        })
        .attr('text-anchor', (d) => {
          return 'middle'
        })
        .text((d) => {
          // console.log(d)
          if (d.depth === 0) {
            return d.data.name
          } else {
            return d.data.name.length > 11 ? d.data.name.substring(0, 11) : d.data.name
          }
        })
        .attr('fill', (d) => {
          if (d.depth === 0) {
            return '#FFFFFF'
          }
          return '#ffffff'
        })
        .style('font-size', (d) => (d.depth === 0 ? 16 : 14))
        .style('font-family', '黑体')
        .style('font-weight', 'bold')
      // 副标题
      node2Enter
        .append('text')
        .attr('class', 'sub-title')
        .attr('x', (d) => {
          return 0
        })
        .attr('y', (d) => {
          return 5
        })
        .attr('text-anchor', (d) => {
          return 'middle'
        })
        .text((d) => {
          if (d.depth !== 0) {
            let subTitle = d.data.name.substring(11)
            if (subTitle.length > 10) {
              return subTitle.substring(0, 10) + '...'
            }
            return subTitle
          }
        })
        .attr('fill', '#FFFFFF')
        .style('font-size', (d) => 14)
        .style('font-family', '黑体')
        .style('font-weight', 'bold')

      // 控股比例
      node2Enter
        .append('text')
        .attr('class', 'percent')
        .attr('x', (d) => {
          return 12
        })
        .attr('y', (d) => {
          return 55
        })
        .text((d) => {
          if (d.depth !== 0) {
            return d.data.percent + '%'
          }
        })
        .attr('fill', '#FFFFFF')
        .style('font-family', '黑体')
        .style('font-size', (d) => 14)

      // 认缴金额
      node2Enter
        .append('text')
        .attr('class', 'sub-title')
        .attr('x', 0)
        .attr('y', 25)
        // .attr('dy', d => d.data.name.substr(10, d.data.name.length).length ? '4.5em' : '4.1em')
        .attr('text-anchor', 'middle')
        .attr('fill', (d) => (d.depth ? '#fff' : '#fff'))
        .text((d) =>
          d.data.rjcze
            ? d.data.rjcze.length > 12
              ? `认缴金额：${d.data.rjcze.substr(0, 10)}…`
              : `认缴金额：${d.data.rjcze}万元`
            : ''
        )
        .style('font-size', '12px')
        .style('font-family', '黑体')
        .style('font-weight', 'bold')
      // 增加展开按钮
      const expandBtnG2 = node2Enter
        .append('g')
        .attr('class', 'expandBtn')
        .attr('transform', (d) => {
          return `translate(${0},${-this.config.rectHeight / 2})`
        })
        .style('display', (d) => {
          // 如果是根节点，不显示
          if (d.depth === 0) {
            return 'none'
          }
          // 如果没有子节点，则不显示
          if (!d._children) {
            return 'none'
          }
        })
        .on('click', (e, d) => {
          if (d.children) {
            d._children = d.children
            d.children = null
          } else {
            d.children = d._children
          }
          this.update(d)
        })

      expandBtnG2.append('circle').attr('r', 8).attr('fill', '#7A9EFF').attr('cy', -8)

      expandBtnG2
        .append('text')
        .attr('text-anchor', 'middle')
        .attr('fill', '#ffffff')
        .attr('y', -3)
        .style('font-size', 16)
        .style('font-family', '微软雅黑')
        .text((d) => {
          return d.children ? '-' : '+'
        })

      const link2 = this.gLinks.selectAll('path.linkOfUpItem').data(linksOfUp, (d) => d.target.data.id)

      const link2Enter = link2
        .enter()
        .append('path')
        .attr('class', 'linkOfUpItem')
        .attr('d', (d) => {
          let o = {
            source: {
              x: source.x0,
              y: source.y0,
            },
            target: {
              x: source.x0,
              y: source.y0,
            },
          }
          return this.drawLink(o)
        })
        .attr('fill', 'none')
        .attr('stroke', '#7A9EFF')
        .attr('stroke-width', 1)
        .attr('marker-end', 'url(#markerOfUp)')

      // 有元素update更新和元素新增enter的时候
      node2
        .merge(node2Enter)
        .transition(myTransition)
        .attr('transform', (d) => {
          return `translate(${d.x},${d.y})`
        })
        .attr('fill-opacity', 1)
        .attr('stroke-opacity', 1)
      // 有元素消失时
      node2
        .exit()
        .transition(myTransition)
        .remove()
        .attr('transform', (d) => {
          return `translate(${source.x0},${source.y0})`
        })
        .attr('fill-opacity', 0)
        .attr('stroke-opacity', 0)
      link2.merge(link2Enter).transition(myTransition).attr('d', this.drawLink)
      link2
        .exit()
        .transition(myTransition)
        .remove()
        .attr('d', (d) => {
          let o = {
            source: {
              x: source.x,
              y: source.y,
            },
            target: {
              x: source.x,
              y: source.y,
            },
          }
          return this.drawLink(o)
        })
      // node数据改变的时候更改一下加减号
      const expandButtonsSelection = this.d3.selectAll('g.expandBtn')
      expandButtonsSelection
        .select('text')
        .transition()
        .text((d) => {
          return d.children ? '-' : '+'
        })

      this.rootOfDown.eachBefore((d) => {
        d.x0 = d.x
        d.y0 = d.y
      })
      this.rootOfUp.eachBefore((d) => {
        d.x0 = d.x
        d.y0 = d.y
      })
    },
    // 直角连接线 by wushengyuan
    drawLink({ source, target }) {
      const halfDistance = (target.y - source.y) / 2
      const halfY = source.y + halfDistance
      return `M${source.x},${source.y} L${source.x},${halfY} ${target.x},${halfY} ${target.x},${target.y}`
    },
    // 展开所有的节点
    expandAllNodes() {
      this.drawChart({
        type: 'all',
      })
    },
    // 将所有节点都折叠
    foldAllNodes() {
      this.drawChart({
        type: 'fold',
      })
    },
    //点击节点获取节点数据
    nodeClickEvent(e, d) {
      // console.log("当前节点的数据：", d)
    },

    // 处理数据
    fmObjToArr(data) {
      let arr = []
      let obj = {
        qyzpfs: '企业总评分数',
        gmzs: '规模指数',
        yysrpf: '营业收入评分',
        yysrxx: '营业收入信息',
        ygrspf: '员工人数评分',
        ygrsxx: '员工人数信息',
        zgsspf: '子公司数评分',
        zgssxx: '子公司数信息',
        cxzs: '创新指数',
        zlslpf: '专利数量评分',
        zlslxx: '专利数量信息',
        yffypf: '研发费用评分',
        yffyxx: '研发费用信息',
        yfrypf: '研发人员评分',
        yfryxx: '研发人员信息',
        czzs: '成长指数',
        yszzpf: '营收增长评分',
        yszzxx: '营收增长信息',
        rjczpf: '人均产值评分',
        rjczxx: '人均产值信息',
        zcfzpf: '资产负债评分',
        zcfzxx: '资产负债信息',
        ryzs: '荣誉指数',
        bqqypf: '百强企业评分',
        bqqyxx: '百强企业信息',
        kjrypf: '科技荣誉评分',
        kjryxx: '科技荣誉信息',
        zljlpf: '质量奖励评分',
        zljlxx: '质量奖励信息',
        fxzz: '风险指数',
        jyfxpf: '经营风险评分',
        aqscpf: '安全生产评分',
        xyfxpf: '信用风险评分',
        mcdjHzrq: '不确定字段',
      }
      let strObj = {
        ssdq: '所属地区',
        zt: '营业状态',
        zczb: '注册资本',
        clrq: '成立日期',
        gszch: '工商注册号',
        ztsfdm: '主体身份代码',
        qydjjg: '企业登记机关',
        qylx: '企业类型',
        jyfw: '经营范围',
        yyqx: '营业期限',
        scjydszxzqh: '生产经营地所在行政区划',
        scjyd: '生产经营地',
        mlhydm: '门类行业代码',
        mlhyZwmc: '门类行业-中文名称',
        jycs: '经营场所',
        zsszd: '住所所在地',
        gslxdh: '公司联系电话',
      }
      for (let key in strObj) {
        arr.push({
          name: this.keyName[key],
          value: key == 'zczb' ? data[key] + data['zczbBz'] : data[key],
        })
      }
      return arr
    },

    format(val) {
      return val.split(',')
    },
  },
}
</script>

<style lang="less" scoped>
ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

.qyhx-center-container {
  width: 3380px;
  height: 1904px;
  padding: 20px 0;
  box-sizing: border-box;
  overflow-x: hidden;
  overflow-y: auto;
  /* background-color: #035b86; */
}

.th_td {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.qyhx-center-top {
  width: 97%;
  font-size: 32px;
  color: #fff;
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
  /* position: relative; */
}

.qyhx-center-top img {
  width: 250px;
  height: 250px;
}

.center {
  width: 100%;
  margin-bottom: 50px;
}

.gs-lx {
  /* width: 49%; */
  max-height: 100%;
  font-size: 32px;
  color: #ffb151;
  overflow: hidden;
  overflow-y: auto;
  padding: 10px 20px;
  box-sizing: border-box;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  box-sizing: border-box;
}

.gs-lx::-webkit-scrollbar {
  width: 0px;
  /*滚动条整体样式*/
  height: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
}

.gs-lx > li {
  margin-bottom: 20px;
  margin-right: 80px;
}

.gs-lx > li:last-child {
  margin-right: 0;
}

.gs-lx > li > span {
  display: inline-block;
  padding: 5px 10px;
  background: rgba(255, 177, 81, 0.102);
  border-radius: 4px 4px 4px 4px;
}

/* 表格自动滚动 */
@keyframes rowUp {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  100% {
    transform: translate3d(0, -100%, 0);
    -webkit-transform: translate3d(0, -100%, 0);
    -moz-transform: translate3d(0, -100%, 0);
    -ms-transform: translate3d(0, -100%, 0);
    -o-transform: translate3d(0, -100%, 0);
  }
}

.scrollrt {
  animation: 5s rowUp linear infinite normal;
  -webkit-animation: 5s rowUp linear infinite normal;
}

.gs-img {
  width: 200px;
  height: 200px;
  margin-right: 100px;
  text-align: center;
  background-color: #3875f6;
  border-radius: 20px;
  color: #fff;
  line-height: 200px;
  font-size: 40px;
  font-weight: bold;
  letter-spacing: 8px;
}

.gs-img > img {
  width: 100%;
  height: 100%;
}

.gs-msg {
  display: flex;
  align-items: center;
}

.gs-name {
  font-size: 60px;
}

.gs-bq {
  display: flex;
  margin-left: 50px;
}

.gs-bq > li {
  height: 60px;
  line-height: 50px;
  padding: 0 30px;
  color: #fff;
  border: 2px solid #5d9cea;
  border-radius: 10px;
  margin: 0 20px;
  box-sizing: border-box;
}

.gs-fr {
  font-size: 48px;
  margin-top: 50px;
}

.jbxx-box {
  width: 100%;
  height: 150px;
}

.jbxx-ul {
  width: 100%;
  font-size: 40px;
  color: #fff;
  display: flex;
  flex-wrap: wrap;
}

.jbxx-ul > li {
  width: 30%;
  margin-bottom: 30px;
}

.legend {
  display: flex;
  font-size: 32px;
  color: #fff;
  position: absolute;
  right: 50px;
  top: 10px;
}

.legend i {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

.legend p {
  margin: 0 30px;
}

.w-100 {
  width: 100% !important;
}

::-webkit-scrollbar {
  width: 4px;
  /*滚动条整体样式*/
  height: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #20aeff;
  height: 8px;
}

/* 新的样式 */

/* 头部 */
.dialog-top {
  font-size: 50px;
  font-weight: bold;
  color: #ffffff;
}

.dialog-top > span {
  background: linear-gradient(0deg, #ffcc00 0.4150390625%, #ffffff 99.5849609375%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 内容 */
.dialog-con {
  display: flex;
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
  height: calc(100% - 66px);
}

.con-right {
  width: 100%;
  height: 100%;
}

.company {
  height: 80px;
  line-height: 80px;
  font-size: 32px;
  font-weight: 500;
  color: #e7f3fa;
}

.company_active {
  font-family: LED;
  font-style: italic;
  background: url('@/pages/qyzf/img/qyhxCenter/gs-bg.png') no-repeat 130px -12px;
}

.company-title {
  display: flex;
}

.qyImg {
  width: 250px;
  height: 250px;
  line-height: 250px;
  font-size: 35px;
  color: #fff;
  border-radius: 10px;
  text-align: center;
  background-color: rgba(0, 192, 255, 0.12);
}

.company-title > img {
  width: 250px;
  height: 250px;
}

.company-title > span {
  font-size: 48px;
  color: #ffffff;
  margin: 90px 100px;
}

.company-tab {
  width: 100%;
  height: 200px;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  margin-top: 30px;
  background: url('@/pages/qyzf/img/qyhxCenter/bg.png') no-repeat;
  background-size: 100% 100%;
}

.qyhx-center-tab_item {
  width: 220px;
  height: 80px;
  background: rgba(10, 97, 158, 0.2);
  border: 1px rgba(236, 244, 255, 0.3) solid;
  border-radius: 5px;
  text-align: center;
  line-height: 80px;
  font-size: 36px;
  color: #ccc;
  cursor: pointer;
}

.tab_active {
  background: rgba(27, 130, 183, 0.22);
  color: #fff;
}

.detail {
  width: 100%;
  /* height: 1365px; */
  margin-top: 65px;
  overflow-y: scroll;
  overflow-x: hidden;
}

.title {
  font-weight: bold;
  color: #f1de77;
  font-size: 48px;
  margin-left: 50px;
  background: linear-gradient(0deg, #ffffff 0%, #00aeff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  border-radius: 5px;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(35, 144, 207, 0.1);
}

::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(27, 146, 215, 0.4);
}

/* 基本信息 */
.jbxx-title {
  font-size: 40px;
  color: #fff;
  display: flex;
  margin: 30px 50px;
}

.jbxx-title .img {
  width: 216px;
  height: 216px;
  border-radius: 50%;
  font-size: 60px;
  line-height: 216px;
  text-align: center;
  color: #fff;
  background: #00c0ff;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.jbxx-title .info > p {
  margin-top: 40px;
  margin-left: 80px;
}

.el-descriptions__body {
  background-color: rgba(0, 0, 0, 0);
  color: #fff;
}

.el-descriptions-item__label.is-bordered-label {
  background-color: rgba(27, 130, 183, 0.22);

  border: 1px solid #7aa3c5;
  color: #fff;
}

.el-descriptions .is-bordered .el-descriptions-item__cell {
  border: 1px solid #7aa3c5;
}

.el-descriptions .el-descriptions-item__label:first-child {
  width: 375px !important;
}

.el-descriptions .el-descriptions-item__content:nth-of-type(1) {
  width: 1400px !important;
}

.el-descriptions .el-descriptions-item__label:nth-of-type(2) {
  width: 375px !important;
}

.el-descriptions {
  font-size: 32px;
}

/* 股东信息 */
.table {
  /* width: 1800px; */
  /* max-height: 550px; */
  padding: 0 10px;
  box-sizing: border-box;
  overflow-y: auto;
  table-layout: fixed;
}

.table .th {
  width: 100%;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-style: italic;
  font-weight: 700;
  font-size: 32px;
  line-height: 80px;
  background: #00396f;
  color: #ffffff;
}

.table .th_td {
  letter-spacing: 0px;
  text-align: center;
}

.table .tbody {
  width: 100%;
  height: calc(100% - 80px);
  /* overflow-y: auto; */
  overflow: hidden;
}

.table .tbody:hover {
  overflow-y: auto;
}

.table .tbody::-webkit-scrollbar {
  width: 4px;
  /*滚动条整体样式*/
  height: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
}

.table .tbody::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #20aeff;
  height: 8px;
}

.table .tr {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80px;
  line-height: 80px;
  font-size: 30px;
  color: #ffffff;
  cursor: pointer;
}

.table .tr:nth-child(2n) {
  background: #00396f;
}

.table .tr:nth-child(2n + 1) {
  background: #035b86;
}

.table .tr:hover {
  background-color: #6990b6;
}

.table .tr_td {
  letter-spacing: 0px;
  text-align: center;
  box-sizing: border-box;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  /* padding: 0 30px; */
}

.zscq_c_blue {
  color: #0ab7ff;
}

/* 放大图表 */
.fd {
  font-size: 80px;
  color: #fff;
  text-align: right;
  position: absolute;
  right: 0;
  top: 60px;
  z-index: 10;
  cursor: pointer;
}

#fullScreenMask {
  width: 100%;
  height: 1200px;
  position: relative;
  /* display: none; */
}

/* new */
.tab-item {
  font-size: 36px;
  color: #ccc;
  font-weight: 700;
  cursor: pointer;
  text-align: center;
  position: relative;
  height: 100%;
}

.tab-active {
  color: #ffffff;
}

.item-name {
  position: absolute;
  width: 100%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.gs-box {
  margin: auto;
  text-align: center;
}

.el-collapse-item__header {
  background: none !important;
  border: none !important;
  height: 70px !important;
  line-height: 70px !important;
  margin-bottom: 30px;
  /* height: 116px !important;
  line-height: 116px !important;
  justify-content: center;
  */
}

.el-collapse-item__wrap {
  background: none !important;
  border: none !important;
  max-height: 1240px !important;
  overflow: auto !important;
}

.el-collapse-item__content {
  padding-bottom: 0 !important;
}

.el-collapse {
  border: none !important;
}

.collapse-item {
  font-size: 40px;
  width: 100%;
  height: 70px;
  background: url('@/pages/qyzf/img/qyhxCenter/收起.png') no-repeat;
  background-size: 100% 100%;
  position: relative;
}

.collapse-item span {
  font-size: 40px;
  font-family: PingFangSC-Semibold-Regular, PingFangSC-Semibold;
  font-weight: 400;
  color: #ccc;
  line-height: 40px;
  letter-spacing: 4px;
  -webkit-background-clip: text;
  margin-left: 70px;
}

.collapseActive {
  background: url('@/pages/qyzf/img/qyhxCenter/展开.png') no-repeat;
  background-size: 100% 100%;
}

.collapseActive span {
  font-family: PingFangSC-Semibold-Regular, PingFangSC-Semibold;
  font-weight: 400;
  background: linear-gradient(to bottom, #ccf4ff, #ffffff, #00baf8, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}

.tab-item:nth-child(1) .item-bg {
  width: 206px;
  height: 160px;
  background: url('@/pages/qyzf/img/qyhxCenter/基本信息.png') no-repeat;
  background-size: 100% 120%;
  background-position: center -30px;
}

.tab-active:nth-child(1) .item-bg {
  width: 206px;
  height: 160px;
  background: url('@/pages/qyzf/img/qyhxCenter/基本信息-active.png') no-repeat;
  background-size: 100% 120%;
  background-position: center -30px;
}

.tab-item:nth-child(2) .item-bg {
  width: 206px;
  height: 160px;
  background: url('@/pages/qyzf/img/qyhxCenter/股东信息.png') no-repeat;
  background-size: 100% 120%;
  background-position: center -30px;
}

.tab-active:nth-child(2) .item-bg {
  width: 206px;
  height: 160px;
  background: url('@/pages/qyzf/img/qyhxCenter/股东信息-active.png') no-repeat;
  background-size: 100% 120%;
  background-position: center -30px;
}

.tab-item:nth-child(3) .item-bg {
  width: 206px;
  height: 160px;
  background: url('@/pages/qyzf/img/qyhxCenter/经营情况.png') no-repeat;
  background-size: 100% 120%;
  background-position: center -30px;
}

.tab-active:nth-child(3) .item-bg {
  width: 206px;
  height: 160px;
  background: url('@/pages/qyzf/img/qyhxCenter/经营情况-active.png') no-repeat;
  background-size: 100% 120%;
  background-position: center -30px;
}

.tab-item:nth-child(4) .item-bg {
  width: 206px;
  height: 160px;
  background: url('@/pages/qyzf/img/qyhxCenter/企业变更.png') no-repeat;
  background-size: 100% 120%;
  background-position: center -30px;
}

.tab-active:nth-child(4) .item-bg {
  width: 206px;
  height: 160px;
  background: url('@/pages/qyzf/img/qyhxCenter/企业变更-active.png') no-repeat;
  background-size: 100% 120%;
  background-position: center -30px;
}

.tab-item:nth-child(5) .item-bg {
  width: 206px;
  height: 160px;
  background: url('@/pages/qyzf/img/qyhxCenter/信用评价.png') no-repeat;
  background-size: 100% 120%;
  background-position: center -30px;
}

.tab-active:nth-child(5) .item-bg {
  width: 206px;
  height: 160px;
  background: url('@/pages/qyzf/img/qyhxCenter/信用评价-active.png') no-repeat;
  background-size: 100% 120%;
  background-position: center -30px;
}

.tab-item:nth-child(5) .item-bg {
  width: 206px;
  height: 160px;
  background: url('@/pages/qyzf/img/qyhxCenter/信用评价.png') no-repeat;
  background-size: 100% 120%;
  background-position: center -30px;
}

.tab-active:nth-child(5) .item-bg {
  width: 206px;
  height: 160px;
  background: url('@/pages/qyzf/img/qyhxCenter/信用评价-active.png') no-repeat;
  background-size: 100% 120%;
  background-position: center -30px;
}

.tab-item:nth-child(6) .item-bg {
  width: 206px;
  height: 160px;
  background: url('@/pages/qyzf/img/qyhxCenter/知识产权.png') no-repeat;
  background-size: 100% 120%;
  background-position: center -30px;
}

.tab-active:nth-child(6) .item-bg {
  width: 206px;
  height: 160px;
  background: url('@/pages/qyzf/img/qyhxCenter/知识产权-active.png') no-repeat;
  background-size: 100% 120%;
  background-position: center -30px;
}

.tab-item:nth-child(7) .item-bg {
  width: 206px;
  height: 160px;
  background: url('@/pages/qyzf/img/qyhxCenter/风险舆情.png') no-repeat;
  background-size: 100% 120%;
  background-position: center -30px;
}

.tab-active:nth-child(7) .item-bg {
  width: 206px;
  height: 160px;
  background: url('@/pages/qyzf/img/qyhxCenter/风险舆情-active.png') no-repeat;
  background-size: 100% 120%;
  background-position: center -30px;
}

.tab-item:nth-child(8) .item-bg {
  width: 206px;
  height: 160px;
  background: url('@/pages/qyzf/img/qyhxCenter/sfaj.png') no-repeat;
  background-size: 100% 120%;
  background-position: center -30px;
}

.tab-active:nth-child(8) .item-bg {
  width: 206px;
  height: 160px;
  background: url('@/pages/qyzf/img/qyhxCenter/sfaj_active.png') no-repeat;
  background-size: 100% 120%;
  background-position: center -30px;
}

.tab-item:nth-child(9) .item-bg {
  width: 206px;
  height: 160px;
  background: url('@/pages/qyzf/img/qyhxCenter/安全生产.png') no-repeat;
  background-size: 100% 120%;
  background-position: center -30px;
}

.tab-active:nth-child(9) .item-bg {
  width: 206px;
  height: 160px;
  background: url('@/pages/qyzf/img/qyhxCenter/安全生产-active.png') no-repeat;
  background-size: 100% 120%;
  background-position: center -30px;
}

.qyhx-center-tit_box {
  display: inline-block;
  background: url('@/pages/qyzf/img/qyhxCenter/fxyj_cen_bg.png') no-repeat;
  background-position-y: bottom;
  padding-left: 36px;
}

/* 知识产权 */
.item_title {
  text-align: center;
  width: 400px;
  height: 75px;
  line-height: 70px;
  background: url('@/pages/qyzf/img/qyhxCenter/zscq_title.png') no-repeat;
  background-position: center;
  background-size: cover;
  margin-bottom: 20px;
}

.item_title > span {
  font-size: 48px;
  font-family: YouSheBiaoTiHei;
  letter-spacing: 1px;
  background: linear-gradient(180deg, #ffffff 0%, #cae2ff 73%, #8facd7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.kj_tip {
  background: #133d6499;
  font-size: 36px;
  color: #fff;
  padding: 20px 50px;
  letter-spacing: 2px;
}

/* 司法案件 */
.sfajList {
  width: 100%;
}

.sfajItem {
  width: calc(100% - 15px);
  height: 274px;
  background: rgba(32, 64, 108, 0.5);
  margin-bottom: 30px;
  padding: 30px;
  box-sizing: border-box;
}

.sfajItem_title {
  font-size: 40px;
  color: #ffffff;
  display: flex;
  position: relative;
}

.to_detail {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 28px;
  color: #91d1f4;
  cursor: pointer;
}

.sfajItem_tags {
  margin: 20px 0;
}

.sfajItem_tags > span {
  background: rgba(11, 255, 254, 0.2);
  font-size: 32px;
  color: #0bfffe;
  border-radius: 8px;
  padding: 0px 20px;
  text-align: center;
  height: 57px;
  line-height: 57px;
  display: inline-block;
  margin-right: 20px;
}

.sfajItem_text {
  font-size: 32px;
  color: rgb(162, 174, 190);
}

.sfajItem_text > span {
  display: inline-block;
  width: 48%;
}

.sfajBack {
  display: block;
  position: absolute;
  top: 10px;
  right: 100px;
}

.ajsllcList {
  font-size: 32px;
  color: #ffffff;
  padding: 0 70px;
  box-sizing: border-box;
}

.ajsllcList > p {
  height: 70px;
  line-height: 70px;
}

.ajsllc_name {
  color: rgb(162, 174, 190);
}

.ajsllc_value {
  color: #91d1f4;
}

.timeLine {
  width: 100%;
  padding: 30px 100px;
  box-sizing: border-box;
}

/* el-timeline */
.el-timeline-item {
  padding-bottom: 50px;
}

.el-timeline-item__timestamp {
  font-size: 32px;
  color: rgb(162, 174, 190);
}

.el-timeline-item__tail {
  border-left: 3px solid #4768a2;
}

.el-timeline-item__node--normal {
  left: -2px;
  width: 16px;
  height: 16px;
}

.el-timeline-item__node {
  background-color: #4768a2;
}

.el-card {
  background: linear-gradient(270deg, rgba(35, 116, 255, 0) 0%, rgba(35, 116, 255, 0.4) 100%);
  border: none;
  color: #fff;
  font-size: 32px;
  cursor: pointer;
}

.card {
  display: flex;
  flex-wrap: wrap;
}

.card_item {
  padding: 0 65px;
  box-sizing: border-box;
}

/************************************   筛选区域的样式 ************************************/

.qyhx-center-select-box {
  width: 100%;
  /* height: 214px; */
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 0 65px 0px 20px;
  box-sizing: border-box;
}

.select-item {
  width: 100%;
  margin-bottom: 20px;
  color: rgba(250, 173, 20, 1);
  font-size: 32px;
}

.select-name {
  width: 200px;
  text-align: right;
  padding-right: 50px;
  box-sizing: border-box;
}

.select-item-wrap {
  width: 100%;
  display: flex;
  justify-content: space-between;
  /* background-color: #00aeff; */
}

.select-list {
  flex: 1;
}

/* 更多 */
.remove-more {
  height: 50px;
  overflow: hidden;
}

.ul-list {
  display: flex;
  flex-wrap: wrap;
}

.ul-list > li {
  margin-right: 50px;
  margin-bottom: 10px;
  /* cursor: pointer; */
}

.ul-list > li:last-child {
  margin-right: 0;
}

.show-more {
  background: linear-gradient(180deg, #17aee0 0%, #0166a6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-left: 50px;
  cursor: pointer;
}

.s-font-38 {
  font-size: 38px;
}

.s-text-center {
  text-align: center;
}

.s-c-white {
  color: #ffffff;
}

.s-w7 {
  font-weight: 700;
}

.s-flex {
  flex-wrap: wrap;
}

.s-row-around {
  justify-content: space-around;
}

.s-m-t-30 {
  margin-top: 30px !important;
}

::v-deep .collapse-item {
  line-height: 70px;
}

::v-deep .el-collapse-item__header {
  background: transparent !important;
  background-color: transparent !important;
  border-bottom: none !important;
  margin: 0 0 40px 0;
}
::v-deep .el-collapse-item__wrap {
  background-color: transparent !important;
  border-bottom: none !important;
}

::v-deep .el-descriptions {
  font-size: 32px;
  .is-bordered .el-descriptions-item__cell {
    border: 1px solid #7aa3c5;
  }
  .el-descriptions-item__label:first-child {
    width: 375px !important;
  }
  .el-descriptions-item__content:nth-of-type(1) {
    width: 1400px !important;
  }
  .el-descriptions-item__label:nth-of-type(2) {
    width: 375px !important;
  }
  .el-descriptions__body {
    background-color: rgba(0, 0, 0, 0);
    color: #fff;
  }
  .el-descriptions-item__label.is-bordered-label {
    background-color: rgba(27, 130, 183, 0.22);

    border: 1px solid #7aa3c5;
    color: #fff;
  }
}
.s-flex {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.s-font-50 {
  font-size: 50px;
}
.s-c-blue-gradient {
  background: linear-gradient(to bottom, #ccf4ff, #ffffff, #00baf8, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}
.s-w7 {
  font-weight: 700;
}
</style>
