const loginTab = [
  {
    path: '/login',
    name: '登录',
    component: () => import('@/pages/login/index'),
    meta: {
      title: '登录',
    },
  },
]
const tabRoutes = [
  {
    path: '/qyzf',
    name: '企业智服',
    component: () => import('@/pages/qyzf/index'),
    meta: {
      title: '企业智服',
    },
  },
  {
    path: '/qyzfCyl',
    name: '产业链',
    component: () => import('@/pages/qyzfCyl/index'),
    meta: {
      title: '产业链',
    },
  },
]
const options = {
  routes: [
    {
      path: '/',
      name: '登录',
      redirect: '/login',
      component: () => import('@/layouts/mainLayoutLogin'),
      children: loginTab,
      meta: {
        title: '登录',
      },
    },
    {
      path: '/qyzf',
      name: '首页',
      redirect: '/qyzf',
      component: () => import('@/layouts/mainLayout'),
      children: tabRoutes,
      meta: {
        title: '首页',
      },
    },
  ],
}
export { tabRoutes, options }
