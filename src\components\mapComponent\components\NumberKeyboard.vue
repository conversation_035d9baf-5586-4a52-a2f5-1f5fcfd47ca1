<template>
  <div class="container">
    <div class="inner">
      <div class="title">
        <div class="name">拨打电话</div>
        <i class="el-icon-close" @click="$emit('close')" style="cursor: pointer"></i>
      </div>
      <div class="inputs">
        <input type="text" class="call_input" v-model="callnum" :maxlength="11"/>
        <img style="cursor: pointer" src="@/assets/common/call_x.png" alt="" @click="clearCallNum()" />
      </div>
      <div class="keyBoard">
        <div class="keyBoard-item" v-for="(item,i) in keyList" :key="i" @click="callNumFun(item)">
          {{item}}
        </div>
      </div>
      <div class="callLine">
        <span class="iconPhone1" @click="call(callnum,'phone')"></span>
        <span class="iconVideo1" @click="call(callnum,'video')"></span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "NumberKeyboard",
  data() {
    return {
      callnum:"",
      keyList: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "*", "0", "#"]
    }
  },
  computed: {},
  mounted() {

  },
  methods: {
    clearCallNum() {
      this.callnum = this.callnum.substring(0, this.callnum.length - 1)
    },
    callNumFun(num) {
      this.callnum += num
    },
    call(num,type) {
      if (num.length > 0) {
        this.$emit("call", {number: num,type: type})
      } else {
        return
      }
    }
  },
  watch: {
    callnum(newVal) {
      // 这里可以做进一步的处理，比如限制只能输入数字加上*号和#号
      this.callnum = newVal.replace(/[^0-9\*#]/g, '');
    }
  }
}
</script>

<style scoped lang="less">
  .container {
    position: absolute;
    top: 678px;
    right: 2178px;
    width: 860px;
    height: 830px;
    background: url("~@/assets/common/dialog-bg.png") no-repeat;
    background-size: 100% 100%;
    .inner {
      margin: 40px 30px 0 40px;
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .name {
          font-size: 48px;
          font-weight: 600;
          background: linear-gradient(180deg, #ffffff 0%, #ffeccb 50.24414063%, #ffc460 53.00292969%, #ffeccb 100%);
          -webkit-background-clip: text;
          color: transparent;
        }
      }
      .inputs {
        width: 100%;
        height: 61px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20px;
        .call_input {
          width: 500px;
          height: 100%;
          background: transparent;
          border: none;
          color: #FFFF;
          font-size: 40px;
          outline: none;
        }
        .call_input::-webkit-outer-spin-button,
        .call_input::-webkit-inner-spin-button {
          -webkit-appearance: none;
        }
      }
      .keyBoard {
        display: flex;
        justify-content: space-around;
        flex-wrap: wrap;
        margin-top: 20px;
        .keyBoard-item {
          width: 220px;
          height: 80px;
          line-height: 80px;
          font-size: 48px;
          color: #fff;
          text-align: center;
          background-color: #053564;
          border-radius: 10px;
          border: solid 2px #074a8c;
          margin-top: 20px;
          cursor: pointer;
        }
        .keyBoard-item:hover {
          background-color: #0d59a0 !important;
        }
      }
      .callLine {
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        margin-top: 46px;
        .iconPhone1 {
          cursor: pointer;
          background: #51c422;
          padding: 8px 15px;
          border-radius: 10px;
          height: 80px;
          width: 300px;
          text-align: center;
          line-height: 110px;
        }
        .iconVideo1 {
          text-align: center;
          cursor: pointer;
          background: #22a0c4;
          padding: 8px 15px;
          border-radius: 10px;
          height: 80px;
          width: 300px;
          line-height: 110px;
          cursor: pointer;
        }

        .iconPhone1::after {
          content: '';
          display: inline-block;
          width: 40px;
          height: 40px;
          background: url("~@/assets/common/icon_call.png") no-repeat;
          background-size: cover;
        }
        .iconVideo1::after {
          content: '';
          display: inline-block;
          width: 40px;
          height: 40px;
          background: url("~@/assets/common/video.png") no-repeat;
          background-size: cover;
        }
      }
    }
  }
</style>