<!--地图场景特效-->
<template>
  <div class="panelContainer">
    <div class="btn back" @click="back">返回</div>

    <div class="btn func" @click="addPointEffect('fire')">启用火焰特效</div>
    <div v-if="effectFlag=='fire'" class="esri-widget" style="padding:12px">请在地图上点选特效位置</div>
    <div class="btn func" @click="removePointEffect('fire')">移除火焰特效</div>

    <div class="btn func" @click="addPointEffect('fog')">启用烟雾特效</div>
    <div v-if="effectFlag=='fog'" class="esri-widget" style="padding:12px">请在地图上点选特效位置</div>
    <div class="btn func" @click="removePointEffect('fog')">移除烟雾特效</div>

    <div class="btn func" @click="addPointEffect('fountain')">启用喷泉特效</div>
    <div v-if="effectFlag=='fountain'" class="esri-widget" style="padding:12px">请在地图上点选特效位置</div>
    <div class="btn func" @click="removePointEffect('fountain')">移除喷泉特效</div>
  </div>
</template>

<script>
export default {
  name: "SceneEffects",
  data() {
    return {
      effectFlag: ''
    }
  },
  computed: {},
  mounted() {

  },
  methods: {
    back() {
      this.$emit('back')
    },
    //场景特效模拟
    addPointEffect(type) {
      let that = this
      this.effectFlag = type
      this.stopPick()
      this.pickPoint((point) => {
        switch (type) {
          case 'fire':
            if (!top.window.fireEffectlocations) top.window.fireEffectlocations = []
            if (top.window.fireEffectlocations.length > 0) top.ArcGisUtils.removeFireEffect()
            top.window.fireEffectlocations.push({
              x: point.lng,
              y: point.lat,
              z: point.z,
              heightLevel: 3,
              sizeLevel: 2,
              color: 'yellow',
            })
            top.ArcGisUtils.addFireEffect(top.window.fireEffectlocations)
            break
          case 'fog':
            if (!top.window.fogEffectlocations) top.window.fogEffectlocations = []
            if (top.window.fogEffectlocations.length > 0) top.ArcGisUtils.removeFireSmokeEffect()
            top.window.fogEffectlocations.push({
              x: point.lng,
              y: point.lat,
              z: point.z,
              heightLevel: 5,
              sizeLevel: 2,
              color: 'yellow',
            })
            top.ArcGisUtils.addFireSmokeEffect(top.window.fogEffectlocations)
            break
          case 'fountain':
            if (!top.window.fountainEffectlocations) top.window.fountainEffectlocations = []
            if (top.window.fountainEffectlocations.length > 0) top.ArcGisUtils.removeFountainEffect()
            top.window.fountainEffectlocations.push({
              x: point.lng,
              y: point.lat,
              z: point.z,
              heightLevel: 0.005,
              sizeLevel: 0.2,
              particleCount: 30000,
              color: 'blue',
            })
            top.ArcGisUtils.addFountainEffect(top.window.fountainEffectlocations)
            break

          default:
            break
        }
      })
    },
    //坐标拾取
    pickPoint(callback) {
      if (!window._pickEvent) {
        window._pickEvent = (e) => {
          if (callback) callback(e)
        }
        top.ArcGisUtils.mapClickEventHandle.addCoordinateListener((point) => {
          const { x, y, z } = point
          if (window._pickEvent) window._pickEvent({ lng: x, lat: y, z: z })
        })
      }
    },
    removePointEffect(type) {
      this.effectFlag = null
      switch (type) {
        case 'fire':
          top.ArcGisUtils.removeFireEffect()
          top.window.fireEffectlocations = []
          break
        case 'fog':
          top.ArcGisUtils.removeFireSmokeEffect()
          top.window.fogEffectlocations = []
          break
        case 'fountain':
          top.ArcGisUtils.removeFountainEffect()
          top.window.fountainEffectlocations = []
          break
        default:
          top.ArcGisUtils.removeFireEffect()
          top.window.fireEffectlocations = []
          top.ArcGisUtils.removeFireSmokeEffect()
          top.window.fogEffectlocations = []
          top.ArcGisUtils.removeFountainEffect()
          top.window.fountainEffectlocations = []
          break
      }
      this.stopPick()
    },
    stopPick() {
      if (window._pickEvent) {
        top.ArcGisUtils.mapClickEventHandle._coordinateClientList = []
        window._pickEvent = null
      }
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
.panelContainer {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  .btn {
    cursor: pointer;
    border: 1px solid hsla(0, 0%, 100%, 0.30980392156862746);
    box-sizing: border-box;
    transition: 0.3s;
    line-height: 30px;
    font-size: 25px;
    background: linear-gradient(180deg, hsla(0, 0%, 100%, 0.4), hsla(0, 0%, 100%, 0));
    color: white;
    text-align: center;
  }
  .back {
    width: 70px;
    height: 35px;
  }
  .func,#toolPanel {
    width: 400px;
    height: 35px;
    margin-top: 15px;
  }
}
</style>