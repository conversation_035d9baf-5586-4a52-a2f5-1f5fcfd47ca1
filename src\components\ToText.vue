<template>
  <div :class="{ 'textContainer': true }">
    <div
      :class="{ 'lineClassOne_O': lineOneType == 1, 'lineClassOne_T': lineOneType == 2, 'lineClassOne_S': lineOneType == 3, 'lineClassOne_F': lineOneType == 4 }">
      {{ text }}</div>
    <div :class="{ 'commonLineTwo': true, 'lineClassTwo_O': lineTwoType == 1, 'lineClassTwo_T': lineTwoType == 2 }">
      <div
        :class="{ 'num_O': numType == 1, 'num_T': numType == 2, 'num_S': numType == 3, 'num_F': numType == 4, 'num_L': numType == 5, 'num_Q': numType == 6 }" class="num">
        {{ num }}</div>
      <div :class="{ 'unit_O': unitType == 1, 'unit_T': unitType == 2, 'unit_S': unitType == 3, 'unit_F': unitType == 4 }">
        {{ unit }}</div>
      <div :class="{ 'tailClass': true }" v-if="showTail">
        <div :class="{ 'tailTxt': true }">{{ tailNum }}</div>
        <img v-if="isUp" :src="require('@/assets/economical/up.png')" class="tailImg">
        <img v-else :src="require('@/assets/economical/down.png')" class="tailImg">
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    lineOneType: {
      type: Number,
      default: 2
    },
    lineTwoType: {
      type: Number,
      default: 0
    },
    numType: {
      type: Number,
      default: 2
    },
    unitType: {
      type: Number,
      default: 2
    },
    text: {
      type: String,
      default: ''
    },
    num: {
      type: String,
      default: ''
    },
    unit: {
      type: String,
      default: ''
    },
    tailNum: {
      type: String,
      default: ''
    },
    showTail: {
      type: Boolean,
      default: false
    },
    isUp: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {

    }
  }
}
</script>
<style scoped lang="less">
.textContainer {
  width: 100%;
  // height: 100%;
  display: flex;
  flex-direction: column;
}

.commonLineTwo {
  display: flex;
  align-items: baseline;
  font-weight: bolder;
}

.lineClassTwo_O {}

.lineClassOne_O {
  font-size: 30px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #FFFFFF;
}

.num {
  font-family: BN;
}

.num_O {
  font-size: 50px;
  margin-right: 8px;
  background: linear-gradient(180deg, #FFFFFF 0%, #CAFFFF 50.244140625%, #22E8E8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.unit_O {
  font-size: 32px;
  background: linear-gradient(180deg, #FFFFFF 0%, #CAFFFF 50.244140625%, #22E8E8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.lineClassOne_T {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #FFFFFF;
  width: 216px;
}

.lineClassTwo_T {}

.num_T {
  font-size: 60px;
  margin-right: 8px;
  background: linear-gradient(180deg, #FFFFFF 0%, #CBF2FF 50.244140625%, #00C0FF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.unit_T {
  font-size: 32px;
  background: linear-gradient(180deg, #FFFFFF 0%, #CBF2FF 50.244140625%, #00C0FF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.lineClassOne_S {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #FFFFFF;
}

.num_S {
  font-size: 50px;
  margin-right: 8px;
  background: linear-gradient(180deg, #FFFFFF 0%, #FFECCB 50.244140625%, #FFC460 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.unit_S {
  font-size: 32px;
  background: linear-gradient(180deg, #FFFFFF 0%, #FFECCB 50.244140625%, #FFC460 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.num_F {
  font-size: 60px;
  margin-right: 8px;
  background: linear-gradient(180deg, #FFFFFF 0%, #CAFFFF 50.244140625%, #22E8E8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.unit_F {
  font-size: 32px;
  color: #fff;
}

.num_L {
  font-size: 50px;
  margin-right: 8px;
  background: linear-gradient(180deg, #FFFFFF 0%, #CBF2FF 50.244140625%, #00C0FF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.lineClassOne_F {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #D6E7F9;
  width: 216px;
}

.num_Q {
  font-size: 60px;
  margin-right: 30px;
  color: #fff;
}




.tailClass {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex: 1;
  height: 100%;
  // background-color: bisque;
  // margin-left: 40px;
}

.tailTxt {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #FFFFFF;
  margin-right: 6px;
  font-weight: bolder;
}

.tailImg {
  width: 19px;
  height: 27px;
}
</style>