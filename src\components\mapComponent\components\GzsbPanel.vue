<template>
  <div class="panelWrap">
    <div class="titleClass">感知设备</div>
    <div class="input-with-select">
      <el-input placeholder="搜索" v-model="searchValue">
        <el-button slot="append" icon="el-icon-search" @click="search"></el-button>
      </el-input>
    </div>
    <div class="treeWrap">
      <div class="treeItem" v-for="item in list" :key="item.value">
        <div class="parentLabel">
          <div class="labeleft">
            <img :class="{'arrowClass':!(showValue == item.value),'arrowLeftClass':showValue == item.value,'noSelect':true}" :src="require(`@/assets/common/xarrow.png`)" @click="showChild(item)">
            <div class="noSelect">{{item.label}}</div>
          </div>
          <img v-show="!item.statu" class="wkuangClass noSelect" :src="require(`@/assets/common/wkuang.png`)" @click="selectCheckBox(item,1)">
          <img v-show="item.statu" class="wkuangClass noSelect" :src="require(`@/assets/common/xkuang.png`)" @click="selectCheckBox(item,1)">
        </div>
        <transition name="mybox">
          <div v-show="showValue == item.value">
            <div class="parentLabel childLabel" v-for="it in item.children" :key="it.value">
              <div class="labeleft">
                <img class="spIconClass noSelect" :src="require(`@/assets/common/sbIcon.png`)">
                <div class="itxt">{{it.label}}</div>
              </div>
              <img v-show="!it.statu" class="wkuangClass noSelect" :src="require(`@/assets/common/wkuang.png`)" @click="selectCheckBox(it,2,item)">
              <img v-show="it.statu" class="wkuangClass noSelect" :src="require(`@/assets/common/xkuang.png`)" @click="selectCheckBox(it,2,item)">
            </div>
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex'
export default {
  data () {
    return {
      searchValue: '',
      list: [
        {
          label: '开发区政法办',
          value: 101,
          statu: 0,
          children: [
            {
              label: '设备A-1',
              value: 201,
              statu: 0,
              lng: 119.65842342884746,
              lat: 29.07890877935061,
              imgType: 'gsjg',
              type:"event",
              lx:"公共安全",
              jd:"发现",
              info: {
                id: '1',
                type: '公共安全',
                desc: '市中心发现可疑包裹',
                time: '2024-4-29 11:12:11',
                jd: '发现',
                color: '#5BB4FF',
                jdindex: 0,
                sjly: '市民报告',
                sjnr: '2024年5月1日上午10点，位于市中心广场附近的步行街，市民陈武兴先生报告发现一个被遗弃的可疑包裹，引起了周围人群的关注和担忧。',
                sjbh: '2024-EVT-0456',
                sjsj: '2024年5月1日 10:00',
                sjdd: '双溪西路',
                sjlx: '公共安全',
                xm: '陈武兴',
                lxdh: '15021781092',
                cldw: '金华市综合行政执法局金华经济技术开发区分局',
                jasj: '暂无',
                jzxx: '暂无',
                tpxx: '暂无',
              },
            },
            {
              label: '设备A-2',
              value: 202,
              statu: 0,
              lng: 119.661945,
              lat: 29.076731,
              imgType: 'gsjg',
              type:"event",
              lx:"公共安全",
              jd:"发现",
              info: {
                id: '1',
                type: '公共安全',
                desc: '市中心发现可疑包裹',
                time: '2024-4-29 11:12:11',
                jd: '发现',
                color: '#5BB4FF',
                jdindex: 0,
                sjly: '市民报告',
                sjnr: '2024年5月1日上午10点，位于市中心广场附近的步行街，市民陈武兴先生报告发现一个被遗弃的可疑包裹，引起了周围人群的关注和担忧。',
                sjbh: '2024-EVT-0456',
                sjsj: '2024年5月1日 10:00',
                sjdd: '双溪西路',
                sjlx: '公共安全',
                xm: '陈武兴',
                lxdh: '15021781092',
                cldw: '金华市综合行政执法局金华经济技术开发区分局',
                jasj: '暂无',
                jzxx: '暂无',
                tpxx: '暂无',
              }
            },
            {
              label: '设备A-3',
              value: 203,
              statu: 0,
              lng: 119.652674,
              lat: 29.071932,
              imgType: 'gsjg',
              type:"event",
              lx:"公共安全",
              jd:"发现",
              info: {
                id: '1',
                type: '公共安全',
                desc: '市中心发现可疑包裹',
                time: '2024-4-29 11:12:11',
                jd: '发现',
                color: '#5BB4FF',
                jdindex: 0,
                sjly: '市民报告',
                sjnr: '2024年5月1日上午10点，位于市中心广场附近的步行街，市民陈武兴先生报告发现一个被遗弃的可疑包裹，引起了周围人群的关注和担忧。',
                sjbh: '2024-EVT-0456',
                sjsj: '2024年5月1日 10:00',
                sjdd: '双溪西路',
                sjlx: '公共安全',
                xm: '陈武兴',
                lxdh: '15021781092',
                cldw: '金华市综合行政执法局金华经济技术开发区分局',
                jasj: '暂无',
                jzxx: '暂无',
                tpxx: '暂无',
              }
            }
          ]
        },
        {
          label: '苏孟乡',
          value: 102,
          statu: 0,
          children: [
            {
              label: '设备E-1',
              value: 205,
              statu: 0,
              lng: 119.660651,
              lat: 29.072122,
              imgType: 'mdjf',
              type:"event",
              lx:"公共安全",
              jd:"发现",
              info: {
                id: '1',
                type: '公共安全',
                desc: '市中心发现可疑包裹',
                time: '2024-4-29 11:12:11',
                jd: '发现',
                color: '#5BB4FF',
                jdindex: 0,
                sjly: '市民报告',
                sjnr: '2024年5月1日上午10点，位于市中心广场附近的步行街，市民陈武兴先生报告发现一个被遗弃的可疑包裹，引起了周围人群的关注和担忧。',
                sjbh: '2024-EVT-0456',
                sjsj: '2024年5月1日 10:00',
                sjdd: '双溪西路',
                sjlx: '公共安全',
                xm: '陈武兴',
                lxdh: '15021781092',
                cldw: '金华市综合行政执法局金华经济技术开发区分局',
                jasj: '暂无',
                jzxx: '暂无',
                tpxx: '暂无',
              }
            },
            {
              label: '设备E-2',
              value: 206,
              statu: 0,
              lng: 119.661154,
              lat: 29.068334,
              imgType: 'mdjf',
              type:"event",
              lx:"公共安全",
              jd:"发现",
              info: {
                id: '1',
                type: '公共安全',
                desc: '市中心发现可疑包裹',
                time: '2024-4-29 11:12:11',
                jd: '发现',
                color: '#5BB4FF',
                jdindex: 0,
                sjly: '市民报告',
                sjnr: '2024年5月1日上午10点，位于市中心广场附近的步行街，市民陈武兴先生报告发现一个被遗弃的可疑包裹，引起了周围人群的关注和担忧。',
                sjbh: '2024-EVT-0456',
                sjsj: '2024年5月1日 10:00',
                sjdd: '双溪西路',
                sjlx: '公共安全',
                xm: '陈武兴',
                lxdh: '15021781092',
                cldw: '金华市综合行政执法局金华经济技术开发区分局',
                jasj: '暂无',
                jzxx: '暂无',
                tpxx: '暂无',
              }
            }
          ]
        },
        {
          label: '秋滨街道',
          value: 103,
          statu: 0,
          children: [
            {
              label: '设备A',
              value: 207,
              statu: 0,
            },
            {
              label: '设备B',
              value: 208,
              statu: 0,
            },
            {
              label: '设备C',
              value: 209,
              statu: 0,
            },
            {
              label: '设备D',
              value: 210,
              statu: 0,
            }]
        }, {
          label: '三江街道',
          value: 104,
          statu: 0,
          children: [
            {
              label: '设备A',
              value: 211,
              statu: 0,
            },
            {
              label: '设备B',
              value: 212,
              statu: 0,
            },
            {
              label: '设备C',
              value: 213,
              statu: 0,
            },
            {
              label: '设备D',
              value: 214,
              statu: 0,
            }]
        }, {
          label: '西关街道',
          value: 105,
          statu: 0,
          children: [
            {
              label: '设备A',
              value: 215,
              statu: 0,
            },
            {
              label: '设备B',
              value: 216,
              statu: 0,
            },
            {
              label: '设备C',
              value: 217,
              statu: 0,
            },
            {
              label: '设备D',
              value: 218,
              statu: 0,
            }]
        }, {
          label: '江南街道',
          value: 106,
          statu: 0,
          children: [
            {
              label: '设备A',
              value: 219,
              statu: 0,
            },
            {
              label: '设备B',
              value: 220,
              statu: 0,
            },
            {
              label: '设备C',
              value: 221,
              statu: 0,
            },
            {
              label: '设备D',
              value: 222,
              statu: 0,
            }]
        }, {
          label: '汤溪镇',
          value: 107,
          statu: 0,
          children: [
            {
              label: '设备A',
              value: 223,
              statu: 0,
            },
            {
              label: '设备B',
              value: 224,
              statu: 0,
            },
            {
              label: '设备C',
              value: 225,
              statu: 0,
            },
            {
              label: '设备D',
              value: 226,
              statu: 0,
            }]
        }, {
          label: '罗埠镇',
          value: 108,
          statu: 0,
          children: [
            {
              label: '设备A',
              value: 227,
              statu: 0,
            },
            {
              label: '设备B',
              value: 228,
              statu: 0,
            },
            {
              label: '设备C',
              value: 229,
              statu: 0,
            },
            {
              label: '设备D',
              value: 230,
              statu: 0,
            }]
        }, {
          label: '洋埠镇',
          value: 109,
          statu: 0,
          children: [
            {
              label: '设备A',
              value: 231,
              statu: 0,
            },
            {
              label: '设备B',
              value: 232,
              statu: 0,
            },
            {
              label: '设备C',
              value: 233,
              statu: 0,
            },
            {
              label: '设备D',
              value: 234,
              statu: 0,
            }]
        }
      ],
      showValue: null
    }
  },
  computed: {
    ...mapGetters(['gzsbCheckList'])
  },
  methods: {
    ...mapMutations('mapStatus', ['GZSB_HISTORY']),
    search () {
    },
    showChild (item) {
      this.showValue == item.value ? this.showValue = null : this.showValue = item.value
    },
    selectCheckBox (row, node, parentRow) {
      const that = this
      if (!row.statu) {
        switch (node) {
          case 1: // 选择父节点
            this.$set(row, 'statu', 1)
            row.children.forEach(ele => {
              this.$set(ele, 'statu', 1)
            })
            break
          case 2: // 选择子节点
            this.$set(row, 'statu', 1)
            let flagArray = parentRow.children.filter(el => {  // 判断子节点是否已全部勾选
              return el.statu == 0
            })
            if (flagArray.length == 0) {
              this.$set(parentRow, 'statu', 1)
            }
            break
        }
        this.addMarker('b', node, row)
      } else {
        switch (node) {
          case 1: // 取消选择父节点
            this.$set(row, 'statu', 0)
            row.children.forEach(ele => {
              this.$set(ele, 'statu', 0)
            })
            break
          case 2: // 取消选择子节点
            this.$set(row, 'statu', 0)
            this.$set(parentRow, 'statu', 0)        // 取消父节点勾选
            break
        }
        this.rmAllLayer(node, row)
      }

    },
    addMarker (type, node, key) {
      this.$EventBus.$emit('drawPoint', type, node, key)
    },
    rmAllLayer (node, key) {
      this.$EventBus.$emit('rmAllLayer', node, key)
    }
  },
  mounted () {
    // console.log('打印历史list', this.gzsbCheckList)
    if (this.gzsbCheckList && this.gzsbCheckList.length > 0) {
      this.list = this.gzsbCheckList
    }
  },
  beforeDestroy () {
    this.GZSB_HISTORY(this.list)
  }
}
</script>

<style lang="less" scoped>
.panelWrap {
  width: 450px;
  height: 1227px;
  background: linear-gradient(180deg, rgba(14, 26, 64, 0.85), rgba(6, 64, 105, 0.85));
  margin-left: 16px;
  display: flex;
  flex-direction: column;

  .titleClass {
    width: 100%;
    height: 64px;
    background: linear-gradient(0deg, #073446, #00aae2);
    font-size: 32px;
    font-family: Source Han Sans CN;
    font-weight: bold;
    color: #ffffff;
    box-sizing: border-box;
    padding: 15px 0 0 45px;
    margin-bottom: 16px;
  }
  .input-with-select {
    padding: 0 14px 0 16px;
    /deep/ .el-input-group {
    }

    /deep/ .el-input__inner {
      background-color: #354c66;
      height: 52px;
      border: 2px solid #cde7fe;
      border-right: none;
      padding-left: 30px;
      font-size: 32px;
      color: #c0d6ed;
    }

    /deep/ .el-input__inner::placeholder {
      font-size: 32px;
      font-family: Source Han Sans SC;
      font-weight: 400;
      color: #c0d6ed;
      padding-top: 10px;
    }

    /deep/ .el-input-group__append {
      padding: 0;
      background-color: #354c66;
      border: 2px solid #cde7fe;
      font-size: 32px;
      color: #c0d6ed;
      padding-right: 20px;
      border-left: none;
    }
  }
  .treeWrap {
    width: 100%;
    flex: 1;
    margin-top: 9px;
    box-sizing: border-box;
    padding: 0 13px 0 8px;
    overflow: hidden;
    overflow-y: auto;
    // background-color: antiquewhite;
    // opacity: 0.5;
    .treeItem {
      .parentLabel {
        margin-top: 12px;
        width: 100%;
        height: 60px;
        background: linear-gradient(94deg, rgba(3, 97, 156, 0) 0%, #03619c 100%);
        border-radius: 0px 30px 30px 0px;
        box-sizing: border-box;
        padding: 0 20px 0 38px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .labeleft {
          display: flex;
          align-items: center;
          font-size: 32px;
          font-family: Source Han Sans SC;
          font-weight: 400;
          color: #ffffff;
          .arrowClass {
            width: 24px;
            height: 16px;
            margin-right: 28px;
            cursor: pointer;
            transition: 0.2s;
            transform-origin: center;
            transform: rotateZ(-90deg);
          }
          .arrowLeftClass {
            width: 24px;
            height: 16px;
            margin-right: 28px;
            cursor: pointer;
            transition: 0.2s;
            transform-origin: center;
            transform: rotateZ(0deg);
          }
          .spIconClass {
            width: 34px;
            height: 26px;
            margin-right: 25px;
          }
        }
        .wkuangClass {
          cursor: pointer;
        }
      }
      .childLabel {
        background: rgba(14, 26, 64, 0);
        transition: 0.3s;
        // cursor: pointer;
        &:hover {
          background: linear-gradient(94deg, rgba(3, 97, 156, 0) 0%, #03619c 100%);
          border-radius: 0px 30px 30px 0px;
        }
        .itxt {
          font-size: 32px;
          font-family: Source Han Sans SC;
          font-weight: 400;
          color: #c0d6ed;
          user-select: none;
        }
      }
    }
  }
}

.noSelect {
  user-select: none;
}

.mybox-leave-active,
.mybox-enter {
  height: 0px;
  opacity: 0;
  transition: 0.3s;
}
.mybox-leave,
.mybox-enter-active {
  height: auto;
  opacity: 1;
  // transition: opacity .3s
  transition: 0.3s;
}
</style>