<!--测量工具-->
<template>
  <div class="container">
    <div class="container-content">
      <div class="container-content-item" :id="item.boxId" v-for="(item,i) in list" :key="i">
        <div class="container-content-item-top">
          <div class="container-content-item-name">{{item.name}}</div>
          <div class="container-content-item-btn" @click="measureClickFun(item,i)">{{item.status}}</div>
        </div>
        <div id="coordPickTool" class="esri-widget" style="padding:12px" v-if="item.name == '坐标拾取' && item.status == '关闭'">
          <div class="info-box" style="margin-top: auto;height: 780px;">
            <div class="coordInfo-text">{{item.coordInfoTitle}}</div>
            <ul class="info-ul">
              <div v-for="obj in coordPickResult">
                <div class="coordInfo-text">{{obj.name}}</div>
                <div class="coordInfo-text">{{obj.lng}}</div>
                <div class="coordInfo-text">{{obj.lat}}</div>
              </div>
            </ul>
          </div>
        </div>
        <div :id="item.id" v-else></div>
      </div>
    </div>
    <div class="container-btn" @click="cleanall">移除测量结果</div>
  </div>
</template>

<script>
export default {
  name: "measure",
  data() {
    return {
      list: [
        {
          name:"面积测量",
          status:"使用",
          id:"areaTool",
          boxId:"area-box"
        },
        {
          name:"距离测量",
          status:"使用",
          id:"disTool",
          boxId:"dis-box"
        },
        {
          name:"角度测量",
          status:"使用",
          id:"angleTool",
          boxId:"angle-box",
          coordInfoTitle:""
        },
        {
          name:"坐标拾取",
          status:"使用",
          coordInfoTitle:""
        },
      ],
      coordPickResult: []
    }
  },
  computed: {},
  mounted() {
    window.angleBtnFun = this.angleBtnFun
  },
  methods: {
    measureClickFun(item,i) {
      let that = this;
      switch (item.name) {
        case '面积测量':
          top.window.areaMeasurementWidget?.destroy()
          top.window.areaMeasurementWidget = null
          if (this.list[i].status == '使用') {
            this.list[i].status = '关闭'
            if (!document.getElementById(item.id)) {
              let widget = top.document.createElement('div')
              widget.id = item.id
              document.getElementById(item.boxId).append(widget)
            }
            debugger
            top.window.areaMeasurementWidget = top.ArcGisUtils.createAreaMeasurementWidget(mapUtil.mapview, document.getElementById(item.id))
          } else {
            this.list[i].status = '使用'
          }
          break;
        case '距离测量':
          top.window.distanceMeasurementWidget?.destroy()
          top.window.distanceMeasurementWidget = null
          if (this.list[i].status == '使用') {
            this.list[i].status = '关闭'
            if (!document.getElementById(item.id)) {
              let widget = top.document.createElement('div')
              widget.id = item.id
              document.getElementById(item.boxId).append(widget)
            }
            top.window.distanceMeasurementWidget = top.ArcGisUtils.measuringDistance(mapUtil.mapview, document.getElementById(item.id))
          } else {
            this.list[i].status = '使用'
          }
          break;
        case '角度测量':
          this.stopAngleMeasure()
          if (this.list[i].status == '使用') {
            this.list[i].status = '关闭'
            top.window.angleMeasurementWidget = new top.ArcGisUtils.AngleMeasure({ view: mapUtil.mapview })
            document.getElementById(item.id).innerHTML = `
                        <div id="angleValue" class="esri-widget"></div>
                        <div class="esri-button" onclick="angleBtnFun()">新测量</div>
                        `
          } else {
            this.list[i].status = '使用'
          }
          break;
        case '坐标拾取':
          if (this.list[i].status == '使用') {
            this.list[i].status = '关闭'
            this.list[i].coordInfoTitle = `请在地图上点选位置`
            if (!top.window.drawTool) top.window.drawTool = new top.ArcGisUtils.Draw({ view: top.mapUtil.mapview })
            top.window.drawTool.draw('point')
            top.window.drawTool.sketchVM.on('create', function (e) {
              if (e.state == 'start') {
                let geometry = e.graphic.geometry
                that.list[i].coordInfoTitle = `CGCS2000坐标：`
                that.coordPickResult.push({
                  name:"点" + (Number(that.coordPickResult.length) + 1),
                  lng:Math.round(geometry.x * 1000000) / 1000000,
                  lat:Math.round(geometry.y * 1000000) / 1000000,
                })
              }
            })
          } else {
            this.stopcoordPick(i)
          }
          break
      }
    },
    stopAngleMeasure() {
      document.getElementById('angleTool').innerHTML = ``
      top.window.angleMeasurementWidget?.destroy()
      top.window.angleMeasurementWidget = null
    },
    angleBtnFun() {
      top.window.angleMeasurementWidget?.destroy()
      top.window.angleMeasurementWidget = null
      if (document.getElementById('angleValue')) document.getElementById('angleValue').innerHTML = '请在地图上绘制夹角'
      top.window.angleMeasurementWidget = new top.ArcGisUtils.AngleMeasure({ view: mapUtil.mapview })
      top.window.angleMeasurementWidget.measure((val) => {
        document.getElementById('angleTool').innerHTML = `<div id="angleValue" class="esri-widget">角度为${val}°</div>
          <div class="esri-button" onclick="angleBtnFun()">新测量</div>
          `
      })
    },
    stopcoordPick(i) {
      this.list[i].status = '使用'
      this.list[i].coordInfoTitle = ''
      this.coordPickResult = []
      this.drawToolDestroy()
    },
    drawToolDestroy() {
      top.window.drawTool?.destroy()
      top.window.drawTool = null
    },
    // 清除测量工具
    async cleanall() {
      //关闭距离测量
      top.window.distanceMeasurementWidget?.destroy()
      top.window.distanceMeasurementWidget = null
      //关闭面积测量
      top.window.areaMeasurementWidget?.destroy()
      top.window.areaMeasurementWidget = null
      //关闭角度测量
      this.stopAngleMeasure()
      //关闭坐标拾取
      this.stopcoordPick(2)
      this.list.forEach(item => {
        item.status = "使用"
      })
    },
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    .container-content {
      height: 1000px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      overflow-y: scroll;
      .container-content-item {
        width: 400px;
        margin-bottom: 15px;
        .container-content-item-top {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .container-content-item-name {
            color: #fff;
            font-size: 30px;
          }
          .container-content-item-btn {
            width: 70px;
            height: 35px;
            cursor: pointer;
            border: 1px solid hsla(0, 0%, 100%, 0.30980392156862746);
            box-sizing: border-box;
            transition: 0.3s;
            line-height: 30px;
            font-size: 25px;
            background: linear-gradient(180deg, hsla(0, 0%, 100%, 0.4), hsla(0, 0%, 100%, 0));
            color: white;
            text-align: center;
          }
          .container-content-item-btn:hover {
            background: #2960cb;
            color: white;
          }
        }
      }
      .info-box {
        height: 100%;
        overflow-x: scroll;
        .info-ul {
          width: 100%;
          height: fit-content;
          margin-bottom: 20px;
        }
        .coordInfo-text {
          color: rgba(255, 255, 255, 0.6);
          font-size: 30px;
        }
      }
    }
    .container-btn {
      width: 400px;
      height: 35px;
      cursor: pointer;
      border: 1px solid hsla(0, 0%, 100%, 0.30980392156862746);
      box-sizing: border-box;
      transition: 0.3s;
      line-height: 30px;
      font-size: 25px;
      background: linear-gradient(180deg, hsla(0, 0%, 100%, 0.4), hsla(0, 0%, 100%, 0));
      color: white;
      text-align: center;
      position: absolute;
      bottom: 30px;
    }
    .container-btn:hover {
      background: #2960cb;
      color: white;
    }
  }
</style>