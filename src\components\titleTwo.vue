<template>
  <div class="title2" :style="{width:width + 'px'}">
    <div class="leftline"></div>
    <div class="lefticon"></div>
    <div class="name">{{titleText}}</div>
    <div class="righticon"></div>
    <div class="rightline"></div>
  </div>
</template>

<script>
export default {
  name: "titleTwo",
  data() {
    return {}
  },
  props:{
    width:{
      type:String,
      default:"935"
    },
    titleText:{
      type:String,
      default:""
    }
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
.title2 {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  .leftline {
    flex: 1;
    height: 2px;
    background: url("../assets/common/title2leftline.png") no-repeat;
    background-size: cover;
  }
  .lefticon {
    width: 57px;
    height: 61px;
    background: url("../assets/common/title2lefticon.png") no-repeat;
    background-size: cover;
  }
  .name {
    font-size: 40px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #D6E7F9;
    text-shadow: 0px 4px 9px rgba(0,0,0,0.29);
    background: linear-gradient(0deg, #CAFFFF 0%, #CAFFFF 0%, #FFFFFF 0%, #00C0FF 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0 13px 0 13px;
  }
  .rightline {
    flex: 1;
    height: 2px;
    background: url("../assets/common/title2leftline.png") no-repeat;
    background-size: cover;
    -moz-transform:scaleX(-1);
    -webkit-transform:scaleX(-1);
    -o-transform:scaleX(-1);
    transform:scaleX(-1);
  }
  .righticon {
    width: 57px;
    height: 61px;
    background: url("../assets/common/title2lefticon.png") no-repeat;
    background-size: cover;
    -moz-transform:scaleX(-1);
    -webkit-transform:scaleX(-1);
    -o-transform:scaleX(-1);
    transform:scaleX(-1);
  }
}
</style>