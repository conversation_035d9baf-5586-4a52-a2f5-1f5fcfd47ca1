<!--通视分析-->
<template>
  <div class="panelContainer">
    <div class="btn back" @click="back">返回</div>
    <div id="toolPanel"></div>
  </div>
</template>

<script>
export default {
  name: "visibilityAnalysis",
  data() {
    return {}
  },
  computed: {},
  mounted() {
    this.init()
  },
  methods: {
    back() {
      this.$emit('back')
    },
    init() {
      top.window.lineOfSightWidget = top.ArcGisUtils.createLineOfSightWidget(
        top.mapUtil.mapview,
        document.getElementById('toolPanel')
      )
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
.panelContainer {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  .btn {
    cursor: pointer;
    border: 1px solid hsla(0, 0%, 100%, 0.30980392156862746);
    box-sizing: border-box;
    transition: 0.3s;
    line-height: 30px;
    font-size: 25px;
    background: linear-gradient(180deg, hsla(0, 0%, 100%, 0.4), hsla(0, 0%, 100%, 0));
    color: white;
    text-align: center;
  }
  .back {
    width: 70px;
    height: 35px;
  }
  .func,#toolPanel {
    width: 400px;
    height: 35px;
    margin-top: 15px;
  }
}
</style>