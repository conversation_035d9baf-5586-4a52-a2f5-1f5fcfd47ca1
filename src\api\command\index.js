import {request} from '@/utils/request'

// 指挥体系列表
export function getZhtxList(query) {
  return request({
    url: '/ywtg-api/ywtg/dp/ywtgZhtx/list',
    method: 'get',
    params: query
  })
}

// 知识库列表
export function getZskList(query) {
  return request({
    url: '/ywtg-api/ywtg/dp/ywtgZsk/list',
    method: 'get',
    params: query
  })
}

// 人员力量列表
export function getRyllList(params) {
  return request({
    url: '/ywtg-api/ywtg/dp/ywtgRyll/list',
    method: 'get',
    params
  })
}

// 人员力量统计
export function getRyllTj() {
  return request({
    url: '/ywtg-api/ywtg/dp/ywtgRyll/ryCount',
    method: 'get'
  })
}

// 储备资源列表
export function getCbzyList(params) {
  return request({
    url: '/ywtg-api/ywtg/dp/ywtgCbzy/list',
    method: 'get',
    params
  })
}

// 储备资源统计
export function getCbzyTj() {
  return request({
    url: '/ywtg-api/ywtg/dp/ywtgCbzy/cbzyCount',
    method: 'get'
  })
}
