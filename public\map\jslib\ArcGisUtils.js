(function () {
  /**
   * 加载SceneView
   * @param {*} urlTemplate
   * @param {*} divId
   */
  async function initSceneView(divId, urlTemplate) {
    return new Promise((resolve, reject) => {
      require([
        'esri/Map',
        'esri/views/SceneView',
        'esri/Basemap',
        'esri/layers/WebTileLayer',
        'esri/layers/support/TileInfo',
        'esri/geometry/Extent',
      ], (esriMap, SceneView, Basemap, WebTileLayer, TileInfo, Extent) => {
        const tileInfo = new TileInfo({
          dpi: 90.71428571427429,
          origin: {
            x: -180,
            y: 90,
          },
          spatialReference: {
            wkid: 4490,
          },
          lods: [
            {
              level: 0,
              levelValue: '1',
              resolution: 0.703125,
              scale: 295497593.05875003,
            },
            {
              level: 1,
              levelValue: '2',
              resolution: 0.3515625,
              scale: 147748796.52937502,
            },
            {
              level: 2,
              levelValue: '3',
              resolution: 0.17578125,
              scale: 73874398.264687508,
            },
            {
              level: 3,
              levelValue: '4',
              resolution: 0.087890625,
              scale: 36937199.132343754,
            },
            {
              level: 4,
              levelValue: '5',
              resolution: 0.0439453125,
              scale: 18468599.566171877,
            },
            {
              level: 5,
              levelValue: '6',
              resolution: 0.02197265625,
              scale: 9234299.7830859385,
            },
            {
              level: 6,
              levelValue: '7',
              resolution: 0.010986328125,
              scale: 4617149.8915429693,
            },
            {
              level: 7,
              levelValue: '8',
              resolution: 0.0054931640625,
              scale: 2308574.9457714846,
            },
            {
              level: 8,
              levelValue: '9',
              resolution: 0.00274658203125,
              scale: 1154287.4728857423,
            },
            {
              level: 9,
              levelValue: '10',
              resolution: 0.001373291015625,
              scale: 577143.73644287116,
            },
            {
              level: 10,
              levelValue: '11',
              resolution: 0.0006866455078125,
              scale: 288571.86822143558,
            },
            {
              level: 11,
              levelValue: '12',
              resolution: 0.00034332275390625,
              scale: 144285.93411071779,
            },
            {
              level: 12,
              levelValue: '13',
              resolution: 0.000171661376953125,
              scale: 72142.967055358895,
            },
            {
              level: 13,
              levelValue: '14',
              resolution: 8.58306884765625e-5,
              scale: 36071.483527679447,
            },
            {
              level: 14,
              levelValue: '15',
              resolution: 4.291534423828125e-5,
              scale: 18035.741763839724,
            },
            {
              level: 15,
              levelValue: '16',
              resolution: 2.1457672119140625e-5,
              scale: 9017.8708819198619,
            },
            {
              level: 16,
              levelValue: '17',
              resolution: 1.0728836059570313e-5,
              scale: 4508.9354409599309,
            },
            {
              level: 17,
              levelValue: '18',
              resolution: 5.3644180297851563e-6,
              scale: 2254.4677204799655,
            },
            {
              level: 18,
              levelValue: '19',
              resolution: 2.68220901489257815e-6,
              scale: 1127.23386023998275,
            },
            {
              level: 19,
              levelValue: '20',
              resolution: 1.341104507446289075e-6,
              scale: 563.616930119991375,
            },
          ],
        });

        const baseLayersConfig = [
          {
            id: 'baseMap',
            title: '金华影像底图',
            urlTemplate:
              urlTemplate ||
              'https://sdi.zjzwfw.gov.cn/services/wmts/imgmap/default/oss?layer=imgmap&style=default&tilematrixset=default028mm&Service=WMTS&Request=GetTile&Version=1.0.0&Format=image/jpgpng&TileMatrix={level}&TileCol={col}&TileRow={row}&token=sy-12da8cc9-b93f-44d0-855b-a89eed57c523',
            subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
            tileInfo: tileInfo,
            spatialReference: { wkid: 4490 },
            fullExtent: {
              xmin: -180,
              ymin: -90,
              xmax: 180,
              ymax: 90,
              spatialReference: { wkid: 4490 },
            },
          },
        ];
        const baseLayers = [];
        for (let i = 0; i < baseLayersConfig.length; i++) {
          const itemLayer = new WebTileLayer(baseLayersConfig[i]);
          baseLayers.push(itemLayer);
        }

        let basemap = new Basemap({
          baseLayers: baseLayers,
        });
        const map = new esriMap({
          basemap: basemap,
        });
        const view = new SceneView({
          container: divId || 'viewDiv',
          map: map,
        });
        view.when(() => {
          const extent = new Extent({
            spatialReference: {
              latestWkid: 4326,
              wkid: 4326,
            },
            xmin: 118.99178803704999,
            ymin: 28.3652455878714,
            xmax: 121.08523403705003,
            ymax: 29.83875398787143,
          });
          view.goTo(extent);
        });
        window.view = view;
        resolve(view);
      });
    });
  }

  /**
   * 加载气象（温度、湿度、云量、风速）等值线图
   * @param {*} type 'temperature'|'humidity'|'clound'|'wind'
   * @param {*} hour  1|3|6||12
   * @retrun {object} {remove:()=>void} 返回移除图层的方法。
   */
  function loadWeatherLayer(type, hour) {
    require(['esri/layers/MapImageLayer'], (MapImageLayer) => {
      const LayerConfig = {
        // 温度
        temperature: [
          {
            hour: 1,
            url: 'https://*************:6443/geoscene/rest/services/JHQX2022113014_t/MapServer',
          },
          {
            hour: 3,
            url: 'https://*************:6443/geoscene/rest/services/JHQX2022113014_t/MapServer',
          },
          {
            hour: 6,
            url: 'https://*************:6443/geoscene/rest/services/JHQX2022113019_t/MapServer',
          },
          {
            hour: 12,
            url: 'https://*************:6443/geoscene/rest/services/JHQX2022120101_t/MapServer',
          },
        ],
        // 湿度
        humidity: [
          {
            hour: 1,
            url: 'https://*************:6443/geoscene/rest/services/JHQX2022113014/MapServer',
          },
          {
            hour: 3,
            url: 'https://*************:6443/geoscene/rest/services/JHQX2022113014/MapServer',
          },
          {
            hour: 6,
            url: 'https://*************:6443/geoscene/rest/services/JHQX2022113019/MapServer',
          },
          {
            hour: 12,
            url: 'https://*************:6443/geoscene/rest/services/JHQX2022120101/MapServer',
          },
        ],
        // 云量
        clound: [
          {
            hour: 1,
            url: 'https://*************:6443/geoscene/rest/services/JHQX2022113014_cloud/MapServer',
          },
          {
            hour: 3,
            url: 'https://*************:6443/geoscene/rest/services/JHQX2022113016_cloud/MapServer',
          },
          {
            hour: 6,
            url: 'https://*************:6443/geoscene/rest/services/JHQX2022113019_cloud/MapServer',
          },
          {
            hour: 12,
            url: 'https://*************:6443/geoscene/rest/services/JHQX2022120101_cloud/MapServer',
          },
        ],
        // 风速
        wind: [
          {
            hour: 1,
            url: 'https://*************:6443/geoscene/rest/services/JHQX2022113014_xWS/MapServer',
          },
          {
            hour: 3,
            url: 'https://*************:6443/geoscene/rest/services/JHQX2022113016_xWS/MapServer',
          },
          {
            hour: 6,
            url: 'https://*************:6443/geoscene/rest/services/JHQX2022113019_xWS/MapServer',
          },
          {
            hour: 12,
            url: 'https://*************:6443/geoscene/rest/services/JHQX2022120101_xWS/MapServer',
          },
        ],
      };

      if (type in LayerConfig) {
        const itemLayerConfig = LayerConfig[type];
        let url;
        for (let i = 0, len = itemLayerConfig.length; i < len; i++) {
          const item = itemLayerConfig[i];
          if (item.hour === hour) {
            url = item.url;
          }
        }
        if (url) {
          const layer = new MapImageLayer(url);
          view.map.add(layer);
          view.whenLayerView(layer).then(() => {
            view.goTo(layer.fullExtent);
          });
          return {
            remove: () => {
              view.map.remove(layer);
            },
          };
        } else {
          console.log('无对应指标的等值线图');
        }
      } else {
        console.log(`无对应指标（${type}）！`);
      }
    });
  }

  function loadSceneLayer() {
    require(["esri/layers/SceneLayer", "esri/layers/IntegratedMeshLayer"], (SceneLayer, IntegratedMeshLayer) => {
      const layerConfigs = [
        {
          type: 'scene',
          url: 'https://*************:6443/geoscene/rest/services/Hosted/obj_all/SceneServer',
        },
        {
          type: 'integrated-mesh',
          url: 'https://*************:6443/geoscene/rest/services/Hosted/SZF4490o/SceneServer',
        },
        {
          type: 'integrated-mesh',
          url: 'https://*************:6443/geoscene/rest/services/Hosted/JB4490o/SceneServer',
        },
        {
          type: 'integrated-mesh',
          url: 'https://*************:6443/geoscene/rest/services/Hosted/JN4490o/SceneServer',
        },
      ];
      const layers = [];
      for (let i = 0, len = layerConfigs.length; i < len; i++) {
        const layerConfig = layerConfigs[i];
        const { url } = layerConfig;
        let itemLayer;
        if (layerConfig.type === 'scene') {
          itemLayer = new SceneLayer({
           
            url,
          });
        } else if (layerConfig.type === 'integrated-mesh') {
          itemLayer = new IntegratedMeshLayer({
        
            url,
          });
        }
        layers.push(itemLayer);
      }
      view.map.addMany(layers);
      view.whenLayerView(layers[0]).then((layerView) => {
        view.goTo(layers[0].fullExtent);
      });
    })
 
  }

  window.ArcGisUtils = { initSceneView, loadWeatherLayer, loadSceneLayer };
})();
