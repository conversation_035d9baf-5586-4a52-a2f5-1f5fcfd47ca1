<!--坐标转换-->
<template>
  <div class="container">
    <div class="btn func container-btn" @click="openFunc" v-if="isOpen == false">打开坐标位转换功能</div>
    <div class="btn func container-btn" @click="close" v-else>关闭坐标位转换功能</div>

    <div class="panelContainer" v-show="isOpen">
      <div class="panelContainer-line">
        <div class="panelContainer-key">坐标系：</div>
        <div class="panelContainer-Value">
          <el-select v-model="wkid" placeholder="请选择坐标系">
            <el-option
              v-for="item in wkidList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="panelContainer-line">
        <div class="panelContainer-key">经纬度坐标：</div>
        <div class="panelContainer-Value">{{zb}}</div>
      </div>
      <div class="panelContainer-line">
        <div class="panelContainer-key">转换后坐标：</div>
        <div class="panelContainer-Value">{{fzb}}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "coordinate",
  data() {
    return {
      wkid:3857,
      isOpen: false,
      wkidList: [
        {
          label:"3857",
          value:3857
        },
        {
          label:"102100",
          value:102100
        }
      ],
      zb:"",
      fzb:""
    }
  },
  computed: {},
  mounted() {

  },
  methods: {
    openFunc() {
      this.isOpen = true
      this.initFunc()
    },
    close() {
      this.isOpen = false
    },
    initFunc() {
      const that = this;
      const view = top.mapUtil.mapview;
      if (view) {
        clearInterval(that.clock);
        view.on('click', async function (event) {
          let point = view.toMap({ x: event.x, y: event.y });
          const { longitude, latitude } = point;
          that.zb = longitude.toFixed(5) + "," + latitude.toFixed(5)
          const { x, y } = await ArcGisUtils.projectPoint({
            longitude,
            latitude,
            outSpatialReferenceWkid: that.wkid,
          });
          that.fzb = x.toFixed(5) + "," + y.toFixed(5)
        });
      }
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .container {
    .panelContainer {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      margin-top: 30px;
      .panelContainer-line {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-top: 20px;
        .panelContainer-key {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: 32px;
          color: #CEF2FF;
          line-height: 40px;
          text-align: left;
          font-style: normal;
          white-space: nowrap;
        }
        .panelContainer-Value {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: 32px;
          color: #FFFFFF;
          line-height: 40px;
          text-align: left;
          font-style: normal;
          word-break: break-all;
        }
      }
    }
    .container-btn:hover {
      background: #2960cb;
      color: white;
    }
    .btn {
      cursor: pointer;
      border: 1px solid hsla(0, 0%, 100%, 0.30980392156862746);
      box-sizing: border-box;
      transition: 0.3s;
      line-height: 30px;
      font-size: 25px;
      background: linear-gradient(180deg, hsla(0, 0%, 100%, 0.4), hsla(0, 0%, 100%, 0));
      color: white;
      text-align: center;
    }
    .func {
      width: 400px;
      height: 35px;
      margin-top: 15px;
    }
  }
</style>