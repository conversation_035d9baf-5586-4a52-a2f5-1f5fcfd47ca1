import {request} from '@/utils/request'

// 预警类型
export function getWarningType() {
  return request({
    url: '/ywtg-api/dp/ywtgYj/dpYjzxYjlxfx',
    method: 'post'
  })
}

// 预警总数分析 type:年1 月2 日3 季4
export function getWarningFx(params) {
  return request({
    url: '/ywtg-api/dp/ywtgYj/dpYjzxYjzsfx',
    method: 'get',
    params
  })
}

// 各类预警趋势 type:年1 月2 日3 季4
export function getWarningQs(params) {
  return request({
    url: '/ywtg-api/dp/ywtgYj/dpYjzxYjqsfx',
    method: 'get',
    params
  })
}

// 首页指标中心
export function getSyzbzx(params) {
  return request({
    url: '/ywtg-api/screen/zbzxDerivativeIndicatorHis/syzbzx',
    method: 'get',
    params
  })
}