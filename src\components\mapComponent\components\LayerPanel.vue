<template>
  <div class="panel-Container">
    <div class="panel-Container-inner">
      <div class="panel-Container-text">图层管理</div>
      <div class="panel-Container-searchLine">
          <div style="flex: 1; height: 100%">
            <el-input v-model="searchValue" clearable @clear="clearSearchValue">
              <el-button slot="append" @click="searchData"></el-button>
            </el-input>
          </div>
          <div class="sou-css" @click="qxALLfun">清除</div>
      </div>
      <div class="tree-container">
        <el-tree
          class="filter-tree"
          ref="tree"
          :data="options"
          :props="defaultProps"
          @check-change="clickChange"
          show-checkbox
          :default-expand-all="expandAll"
          :render-after-expand="false"
          node-key="id"
          :filter-node-method="filterNode"
          :check-strictly="true">
          <span class="filter-tree" style="display: flex; align-items: center" slot-scope="{ node, data }">
            <span v-if="data.icon=='#icon-wenjianjia'"
                  :class="['icon','s-m-l-10',node.expanded?'open-file':'close-file']" aria-hidden="true"></span>
            <span :class="[node.disabled?'point-no':'','s-m-l-10']" style="font-size: 1.875rem; line-height: 3.125rem"
                  :title="node.label">
              {{ node.label }}
              <span v-if="data.icon==='#icon-wenjianjia'">({{data.number}})</span>
            </span>
          </span>
        </el-tree>
      </div>
    </div>
    <Commondialog :dialog-flag="showDialog" :title="dialogTitle" @close="showDialog = false">
      <CommonDialogDetail2 :list="PointObjList" ></CommonDialogDetail2>
    </Commondialog>
  </div>
</template>

<script>
import axios from "axios";
import Commondialog from "@/components/Commondialog";
import CommonDialogDetail2 from "@/components/CommonDialogDetail2";
import mapData from '@/data/rrcsj_map.json'
import kfqData from '@/data/kfq.json'
import {getCsdnInterface, layerPoiSearch} from "@/api/csdnIndexApi";
export default {
  name: "LayerPanel",
  data() {
    return {
      searchValue:"",
      expandAll: false,
      options: [
        {
          icon: '#icon-wenjianjia',
          children: [
            {
              "id": 7,
              "category": "公共服务设施",
              "name": "影剧院",
              "sub_category": "180202",
              "active": "1",
              "order_num": 1,
              "code": "1",
              "isclick": "1",
              "icon": "rckz-影剧院",
              "pid": 100003
            },
            {
              "id": 21,
              "category": "公共服务设施",
              "name": "公园广场",
              "sub_category": "180303,180402,180404,180405,180307",
              "active": "1",
              "order_num": 2,
              "code": "1",
              "isclick": "1",
              "icon": "rckz-公园广场",
              "pid": 100003
            },
            {
              "id": 270,
              "category": "公共服务设施",
              "name": "文化馆",
              "sub_category": "文化馆",
              "active": "1",
              "order_num": 13,
              "url": "yxzl_szwh_center011",
              "code": "5",
              "isclick": "1",
              "icon": "szwh-文化馆",
              "pid": 100003
            },
            {
              "id": 272,
              "category": "公共服务设施",
              "name": "图书馆",
              "sub_category": "图书馆",
              "active": "1",
              "order_num": 14,
              "url": "yxzl_szwh_center011",
              "code": "5",
              "isclick": "1",
              "icon": "szwh-图书馆",
              "pid": 100003
            },
            {
              "id": 274,
              "category": "公共服务设施",
              "name": "博物馆",
              "sub_category": "博物馆",
              "active": "1",
              "order_num": 15,
              "url": "yxzl_szwh_center011",
              "code": "5",
              "isclick": "1",
              "icon": "szwh-博物馆",
              "pid": 100003
            },
            {
              "id": 18,
              "category": "公共服务设施",
              "name": "学校",
              "sub_category": "160101,160102,160103,160104,160105,160106,160107",
              "active": "1",
              "order_num": 16,
              "code": "1",
              "isclick": "1",
              "icon": "rckz-学校",
              "pid": 100003
            },
            {
              "id": 17,
              "category": "公共服务设施",
              "name": "医院",
              "sub_category": "170101,170102,170103,170104,170105",
              "active": "1",
              "order_num": 18,
              "code": "1",
              "isclick": "1",
              "icon": "rckz-医院",
              "pid": 100003
            },
            {
              "id": 22,
              "category": "公共服务设施",
              "name": "公交车站",
              "sub_category": "230105",
              "active": "1",
              "order_num": 50,
              "code": "1",
              "isclick": "1",
              "icon": "rckz-公交车站",
              "pid": 100003
            }
          ],
          "category": "公共服务设施",
          "name": "公共服务设施",
          "pid": 0,
          "number": 8,
        },
        {
          icon: '#icon-wenjianjia',
          children: [
            {
              "id": 1722,
              "category": "2023",
              "name": "水质监测",
              "sub_category": "",
              "active": "1",
              "order_num": 30,
              "url": "https://csdnwlgz.dsjj.jinhua.gov.cn/server2/rest/services/result_Mn_20230821_tif/MapServer",
              "code": 2,
              "isclick": "1",
              "pid": 1510,
              "isDirectory": "1",
              "layertype": "map-image",
              "layerurl": "https://csdnwlgz.dsjj.jinhua.gov.cn/server2/rest/services/result_Mn_20230821_tif/MapServer",
              "number": 0
            },
            {
              "id": 1301,
              "category": "2022",
              "name": "地表水系监测",
              "sub_category": "",
              "active": "1",
              "order_num": 1,
              "url": "https://csdnwlgz.dsjj.jinhua.gov.cn/server2/rest/services/water_202201TIFF/MapServer",
              "code": 2,
              "isclick": "1",
              "pid": 1469,
              "isDirectory": "1",
              "layertype": "map-image",
              "layerurl": "https://csdnwlgz.dsjj.jinhua.gov.cn/server2/rest/services/water_202201TIFF/MapServer",
              "sublayers": "",
              "number": 0
            },
            {
              "id": 1515,
              "category": "2023",
              "name": "植被覆盖监测",
              "sub_category": "",
              "active": "1",
              "order_num": 1,
              "url": "https://csdn.dsjj.jinhua.gov.cn:8101/geoserver/JHRS/wms?service=WMS&version=1.1.0&request=GetMap&layers=JHRS%3Andvi_20230108&bbox=119.19877292448886%2C28.50615361460483%2C120.7948374566814%2C29.70204272845201&width=768&height=575&srs=EPSG%3A4326&format=application/openlayers",
              "code": 2,
              "isclick": "1",
              "pid": 1514,
              "isDirectory": "1",
              "layertype": "wms",
              "layerurl": "https://csdn.dsjj.jinhua.gov.cn:8101/geoserver/JHRS/wms",
              "sublayers": "JHRS:ndvi_20230108",
              "number": 0
            },
            {
              "id": 1319,
              "category": "2022",
              "name": "裸土监测",
              "sub_category": "",
              "active": "1",
              "order_num": 1,
              "url": "https://csdnwlgz.dsjj.jinhua.gov.cn/server2/rest/services/luotu_20220120/MapServer",
              "code": 2,
              "isclick": "1",
              "pid": 1472,
              "isDirectory": "1",
              "layertype": "map-image",
              "layerurl": "https://csdnwlgz.dsjj.jinhua.gov.cn/server2/rest/services/luotu_20220120/MapServer",
              "sublayers": "",
              "number": 0
            },
            {
              "id": 1306,
              "category": "PM2.5年均浓度监测",
              "name": "PM2.5年均浓度监测",
              "sub_category": "",
              "active": "1",
              "order_num": 10,
              "url": "https://csdnwlgz.dsjj.jinhua.gov.cn/server2/rest/services/PM_2019TIFF/MapServer",
              "code": 2,
              "isclick": "1",
              "pid": 1368,
              "isDirectory": "1",
              "layertype": "map-image",
              "layerurl": "https://csdnwlgz.dsjj.jinhua.gov.cn/server2/rest/services/PM_2019TIFF/MapServer",
              "sublayers": "",
              "number": 0
            },
            {
              "id": 1758,
              "category": "2023",
              "name": "地面沉降监测",
              "sub_category": "",
              "active": "1",
              "order_num": 30,
              "url": "https://csdn.dsjj.jinhua.gov.cn:8101/geoserver/JHRS/wms?service=WMS&version=1.1.0&request=GetMap&layers=JHRS%3AChenJiang_202310-202312&bbox=119.56679185478791%2C29.039528017528383%2C119.7191245245927%2C29.13651575331731&width=768&height=488&srs=EPSG%3A4326&format=application/openlayers",
              "code": 2,
              "isclick": "1",
              "pid": 1609,
              "isDirectory": "1",
              "layertype": "wms",
              "layerurl": "https://csdn.dsjj.jinhua.gov.cn:8101/geoserver/JHRS/wms",
              "sublayers": "JHRS:ChenJiang_202310-202312",
              "number": 0
            },
          ],
          "category": "测绘遥感数据",
          "name": "测绘遥感数据",
          "pid": 0,
          "number": 6,
        },
        {
          icon: '#icon-wenjianjia',
          children: [
            {
            "id": 1339,
            "category": "2022",
            "name": "土壤墙情",
            "sub_category": "",
            "active": "1",
            "order_num": 1,
            "url": "https://csdn.dsjj.jinhua.gov.cn:8101/geoserver/JHRS/wms?service=WMS&version=1.1.0&request=GetMap&layers=JHRS%3Atdvi_20220103&bbox=119.20248000810334%2C28.488559217416856%2C120.798144709225%2C29.69702243774083&width=768&height=581&srs=EPSG%3A4326&format=application/openlayers",
            "code": 2,
            "isclick": "1",
            "pid": 1474,
            "isDirectory": "1",
            "layertype": "wms",
            "layerurl": "https://csdn.dsjj.jinhua.gov.cn:8101/geoserver/JHRS/wms",
            "sublayers": "JHRS:tdvi_20220103",
            "number": 0
          },
            {
              "id": 1596,
              "category": "2023",
              "name": "多光谱影像",
              "sub_category": "",
              "active": "1",
              "order_num": 1,
              "url": "https://csdnwlgz.dsjj.jinhua.gov.cn/server2/rest/services/MSI20230108_S2_10TIFF/MapServer",
              "code": 2,
              "isclick": "1",
              "pid": 1595,
              "isDirectory": "1",
              "layertype": "map-image",
              "layerurl": "https://csdnwlgz.dsjj.jinhua.gov.cn/server2/rest/services/MSI20230108_S2_10TIFF/MapServer",
              "number": 0
            },
            {
              "id": 1590,
              "category": "2023",
              "name": "雷达影像",
              "sub_category": "",
              "active": "1",
              "order_num": 1,
              "url": "https://csdnwlgz.dsjj.jinhua.gov.cn/server2/rest/services/SAR20230108_S1A_15TIFF/MapServer",
              "code": 2,
              "isclick": "1",
              "pid": 1589,
              "isDirectory": "1",
              "layertype": "map-image",
              "layerurl": "https://csdnwlgz.dsjj.jinhua.gov.cn/server2/rest/services/SAR20230108_S1A_15TIFF/MapServer",
              "number": 0
            }],
          "category": "高分影像专题",
          "name": "高分影像专题",
          "pid": 0,
          "number": 3,
        },
        {
          icon: '#icon-wenjianjia',
          children: [
            {
              "id": 1559,
              "category": "BIM模型",
              "name": "BIM模型_金华之心结构",
              "sub_category": "",
              "active": "1",
              "order_num": 1,
              "url": "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BIM_ALL_001/SceneServer",
              "code": 2,
              "isclick": "1",
              "icon": "rckz-楼宇",
              "pid": 1557,
              "isDirectory": "1",
              "layertype": "building-scene",
              "layerurl": "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BIM_ALL_001/SceneServer",
              "number": 0
            },
            {
              "id": 1601,
              "category": "BIM模型",
              "name": "BIM模型_金华之心幕墙",
              "sub_category": "",
              "active": "1",
              "order_num": 30,
              "layertype": "building-scene",
              "url": "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BIM_MQ_123/SceneServer",
              "layerurl": "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BIM_MQ_123/SceneServer",
              "code": "2",
              "isclick": "1",
              "icon": "rckz-楼宇",
              "pid": 100010
            },
          ],
          "category": "BIM模型",
          "name": "BIM模型",
          "pid": 0,
          "number": 2,
        },
        {
          icon: '#icon-wenjianjia',
          children: [
            {
              "id": 222,
              "category": "工矿及其设施",
              "name": "重大火灾隐患",
              "sub_category": "zdhzyh",
              "active": "1",
              "order_num": 1,
              "url": "/csdnMap/jhyjzh-server/screen_api/tcgl/mapDzzhyhd",
              "code": 6,
              "isclick": "1",
              "pid": 1216,
              "isDirectory": "1",
              "number": 0
            },
            {
              "id": 223,
              "category": "工矿及其设施",
              "name": "危险化学品企业",
              "sub_category": "",
              "active": "1",
              "order_num": 2,
              "url": "/csdnMap/jhyjzh-server/screen_api/tcgl/mapWhpqy",
              "code": 7,
              "isclick": "1",
              "pid": 1216,
              "isDirectory": "1",
              "number": 0
            },
          ],
          "category": "工矿及其设施",
          "name": "工矿及其设施",
          "pid": 0,
          "number": 2,
        },
        {
          icon: '#icon-wenjianjia',
          children: [
            {
              "id": 1013,
              "category": "行政区网格",
              "name": "网格_开发区",
              "sub_category": "",
              "active": "1",
              "order_num": 10,
              "code": 8,
              "isclick": "1",
              "pid": 1126,
              "isDirectory": "1",
              "number": 0
            },
          ],
          "category": "行政区网格",
          "name": "行政区网格",
          "pid": 0,
          "number": 1,
        },
        {
          icon: '#icon-wenjianjia',
          children: [
            {
              "id": 246,
              "category": "排水设施",
              "name": "市政污水处理设施点位图",
              "sub_category": "",
              "active": "1",
              "order_num": 1,
              "url": "/csdnMap/jhyjzh-server/screen_api/tcgl/wxjk",
              "code": 6,
              "isclick": "1",
              "pid": 1157,
              "isDirectory": "1",
              "number": 0
            }
          ],
          "category": "排水设施",
          "name": "排水设施",
          "pid": 0,
          "number": 1,
        },
        {
          icon: '#icon-wenjianjia',
          children: [
            {
              "id": 1255,
              "category": "倾斜影像",
              "name": "倾斜摄影_市政府",
              "sub_category": "",
              "active": "1",
              "order_num": 4,
              "url": "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/szf_Project/SceneServer",
              "code": 2,
              "isclick": "1",
              "icon": "icon-倾斜摄影(市政府)",
              "pid": 1130,
              "isDirectory": "1",
              "layertype": "integrated-mesh",
              "layerurl": "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/szf_merge/SceneServer",
              "number": 0
            },
            {
              "id": 1745,
              "category": "倾斜影像",
              "name": "倾斜摄影_金华之心",
              "sub_category": "",
              "active": "1",
              "order_num": 30,
              "url": "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/qxsy_jhzx_eslpk/SceneServer",
              "code": 2,
              "isclick": "1",
              "icon": "icon-倾斜摄影(亚运分村)",
              "pid": 1130,
              "isDirectory": "1",
              "layertype": "integrated-mesh",
              "layerurl": "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/qxsy_jhzx_eslpk/SceneServer",
              "number": 0
            },
            {
              "id": 38,
              "category": "倾斜影像",
              "name": "倾斜摄影_亚运分村",
              "sub_category": "",
              "active": "1",
              "order_num": 5,
              "url": "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/qxsy_eslpk/qxsy_yyc_mer_pro_cl/SceneServer",
              "code": 2,
              "isclick": "1",
              "icon": "icon-倾斜摄影(亚运分村)",
              "pid": 1130,
              "isDirectory": "1",
              "layertype": "integrated-mesh",
              "layerurl": "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/qxsy_eslpk/qxsy_yyc_mer_pro_cl/SceneServer",
              "number": 0
            },
          ],
          "category": "倾斜影像",
          "name": "倾斜影像",
          "pid": 0,
          "number": 3,
        },
        // {
        //   icon: '#icon-wenjianjia',
        //   children: [{
        //     "id": 1025,
        //     "category": "桥梁设施",
        //     "name": "桥梁",
        //     "sub_category": "",
        //     "active": "1",
        //     "order_num": 1,
        //     "url": "https://yt.zjasm.net/apigw/services/arcgisrest/gsgl/latest/sy-6786f525-a9bd-477f-ab4a-f26d29524077/export?layers=show:0&transparent=true&bbox=&SRS=EPSG:4490&STYLES=&WIDTH=256&HEIGHT=256&f=image&BBOX={bbox-epsg-4490}",
        //     "code": 2,
        //     "isclick": "1",
        //     "pid": 1155,
        //     "isDirectory": "1",
        //     "layertype": "feature",
        //     "layerurl": "https://yt.zjasm.net/apigw/services/arcgisrest/gsgl/latest/sy-6786f525-a9bd-477f-ab4a-f26d29524077/0",
        //     "number": 0
        //   }],
        //   "category": "桥梁设施",
        //   "name": "桥梁设施",
        //   "pid": 0,
        //   "number": 1,
        // },
        {
          icon: '#icon-wenjianjia',
          children: [
            {
            "id": 1000,
            "category": "基层治理单元",
            "name": "未来乡村",
            "sub_category": "1",
            "active": "1",
            "order_num": 1,
            "url": "tcgl_wlsq",
            "code": 5,
            "isclick": "1",
            "icon": "村庄",
            "pid": 1215,
            "isDirectory": "1",
            "number": 0
          },
            {
              "id": 1001,
              "category": "基层治理单元",
              "name": "未来社区",
              "sub_category": "2",
              "active": "1",
              "order_num": 2,
              "url": "tcgl_wlsq",
              "code": 5,
              "isclick": "1",
              "icon": "rckz-住宅区",
              "pid": 1215,
              "isDirectory": "1",
              "number": 0
            }],
          "category": "基层治理单元",
          "name": "基层治理单元",
          "pid": 0,
          "number": 2,
        },
        {
          icon: '#icon-wenjianjia',
          children: [
            {
              "id": 265,
              "category": "旅游",
              "name": "星级酒店",
              "sub_category": "星级酒店",
              "active": "1",
              "order_num": 1,
              "url": "yxzl_szwh_center011",
              "code": 5,
              "isclick": "1",
              "icon": "szwh-星级酒店",
              "pid": 1214,
              "isDirectory": "1",
              "number": 0
            },
            {
              "id": 266,
              "category": "旅游",
              "name": "等级民宿",
              "sub_category": "等级民宿",
              "active": "1",
              "order_num": 2,
              "url": "yxzl_szwh_center011",
              "code": 5,
              "isclick": "1",
              "icon": "szwh-等级民宿",
              "pid": 1214,
              "isDirectory": "1",
              "number": 0
            },
            {
              "id": 267,
              "category": "旅游",
              "name": "A级及以上景区",
              "sub_category": "景区",
              "active": "1",
              "order_num": 3,
              "url": "yxzl_szwh_center011",
              "code": 5,
              "isclick": "1",
              "icon": "szwh-景区",
              "pid": 1214,
              "isDirectory": "1",
              "number": 0
            },
            {
              "id": 268,
              "category": "旅游",
              "name": "旅行社",
              "sub_category": "旅行社",
              "active": "1",
              "order_num": 4,
              "url": "yxzl_szwh_center011",
              "code": 5,
              "isclick": "1",
              "icon": "szwh-旅行社",
              "pid": 1214,
              "isDirectory": "1",
              "number": 0
            },
            {
              "id": 269,
              "category": "旅游",
              "name": "景区厕所",
              "sub_category": "厕所",
              "active": "1",
              "order_num": 5,
              "url": "yxzl_szwh_center011",
              "code": 5,
              "isclick": "1",
              "icon": "szwh-厕所",
              "pid": 1214,
              "isDirectory": "1",
              "number": 0
            },
            // {
            //   "id": 684,
            //   "category": "旅游",
            //   "name": "景区信息-文字",
            //   "sub_category": "",
            //   "active": "1",
            //   "order_num": 6,
            //   "url": "https://yt.zjasm.net/apigw2/services/arcgisdynamicmapservicelayer/2ff00af335aa4da69383c948825908dc/latest/sy-6786f525-a9bd-477f-ab4a-f26d29524077/export?transparent=true&bbox=&SRS=EPSG:4490&STYLES=&WIDTH=256&HEIGHT=256&f=image&BBOX={bbox-epsg-4490}",
            //   "code": 2,
            //   "isclick": "1",
            //   "pid": 1214,
            //   "isDirectory": "1",
            //   "layertype": "map-image",
            //   "number": 0
            // }
          ],
          "category": "旅游",
          "name": "旅游",
          "pid": 0,
          "number": 6,
        },
        {
          icon: '#icon-wenjianjia',
          children: [
            {
              "id": 417,
              "category": "城镇规划",
              "name": "乡镇范围线",
              "sub_category": "",
              "active": "1",
              "order_num": 1,
              "url": "https://yt.zjasm.net/apigw2/services/arcgisdynamicmapservicelayer/d15edc7d8e37418b913f5b790b1065ae/latest/sy-6786f525-a9bd-477f-ab4a-f26d29524077/export?layers=show:9&transparent=true&bbox=&SRS=EPSG:4490&STYLES=&WIDTH=256&HEIGHT=256&f=image&BBOX={bbox-epsg-4490}",
              "code": 2,
              "isclick": "1",
              "pid": 1175,
              "isDirectory": "1",
              "layertype": "map-image",
              "layerurl": "https://yt.zjasm.net/apigw2/services/arcgisdynamicmapservicelayer/d15edc7d8e37418b913f5b790b1065ae/latest/sy-6786f525-a9bd-477f-ab4a-f26d29524077",
              "sublayers": "9",
              "number": 0
            },
            {
              "id": 575,
              "category": "城镇规划",
              "name": "城镇道路中心线",
              "sub_category": "",
              "active": "1",
              "order_num": 2,
              "url": "/csdnMap/jhgt/jhgt/f132cca0231645b892d1c15318a512d3/arcgis/rest/services/GCS330700_2007_CSZTGH/GCS330700_2007_ZG_CZDLZXX/MapServer/export?transparent=true&bbox=&SRS=EPSG:4490&STYLES=&WIDTH=256&HEIGHT=256&f=image&BBOX={bbox-epsg-4490}",
              "code": 2,
              "isclick": "1",
              "pid": 1175,
              "isDirectory": "1",
              "layertype": "map-image",
              "layerurl": "/csdnMap/jhgt/jhgt/f132cca0231645b892d1c15318a512d3/arcgis/rest/services/GCS330700_2007_CSZTGH/GCS330700_2007_ZG_CZDLZXX/MapServer",
              "number": 0
            },
            {
              "id": 577,
              "category": "城镇规划",
              "name": "城市橙线",
              "sub_category": "",
              "active": "1",
              "order_num": 3,
              "url": "/csdnMap/jhgt/jhgt131/96a3d7ffd14f4ab08a5724eee6f6d8ab/arcgis/rest/services/GCS330700G2007_CSZTGH/GCS330700G2007_ZG_CSCX_2022/MapServer/export?transparent=true&bbox=&SRS=EPSG:4490&STYLES=&WIDTH=256&HEIGHT=256&f=image&BBOX={bbox-epsg-4490}",
              "code": 2,
              "isclick": "1",
              "pid": 1175,
              "isDirectory": "1",
              "layertype": "map-image",
              "layerurl": "/csdnMap/jhgt/jhgt131/96a3d7ffd14f4ab08a5724eee6f6d8ab/arcgis/rest/services/GCS330700G2007_CSZTGH/GCS330700G2007_ZG_CSCX_2022/MapServer",
              "number": 0
            },
            {
              "id": 578,
              "category": "城镇规划",
              "name": "道路红线",
              "sub_category": "",
              "active": "1",
              "order_num": 4,
              "url": "/csdnMap/jhgt/jhgt131/2ed1b5f1539644dd9a3effe154a4f5a2/arcgis/rest/services/GCS330700G2007_CSZTGH/GCS330700G2007_ZG_DLHX_2022/MapServer/export?transparent=true&bbox=&SRS=EPSG:4490&STYLES=&WIDTH=256&HEIGHT=256&f=image&BBOX={bbox-epsg-4490}",
              "code": 2,
              "isclick": "1",
              "pid": 1175,
              "isDirectory": "1",
              "layertype": "map-image",
              "layerurl": "/csdnMap/jhgt/jhgt131/2ed1b5f1539644dd9a3effe154a4f5a2/arcgis/rest/services/GCS330700G2007_CSZTGH/GCS330700G2007_ZG_DLHX_2022/MapServer",
              "number": 0
            },
            {
              "id": 408,
              "category": "城镇规划",
              "name": "城镇开发边界（最新试划）",
              "sub_category": "",
              "active": "1",
              "order_num": 5,
              "url": "https://yt.zjasm.net/apigw2/services/arcgisdynamicmapservicelayer/c7ef69230cf64a9ca9f2060a414df85c/latest/sy-6786f525-a9bd-477f-ab4a-f26d29524077/export?layers=show:1&&transparent=true&bbox=&SRS=EPSG:4490&STYLES=&WIDTH=256&HEIGHT=256&f=image&BBOX={bbox-epsg-4490}",
              "code": 2,
              "isclick": "1",
              "pid": 1175,
              "isDirectory": "1",
              "layertype": "map-image",
              "layerurl": "https://yt.zjasm.net/apigw2/services/arcgisdynamicmapservicelayer/c7ef69230cf64a9ca9f2060a414df85c/latest/sy-6786f525-a9bd-477f-ab4a-f26d29524077",
              "number": 0
            },
            {
              "id": 539,
              "category": "城镇规划",
              "name": "城镇开发边界（2022）",
              "sub_category": "",
              "active": "1",
              "order_num": 6,
              "url": "/csdnMap/jhgt/jhgt131/4cfe4df3e41c4912973927c2f4a626be/arcgis/rest/services/CS330700G2001_SQSX/GCS330700G2001_SQSX_CZKFBJ_2022/MapServer/export?transparent=true&bbox=&SRS=EPSG:4490&STYLES=&WIDTH=256&HEIGHT=256&f=image&BBOX={bbox-epsg-4490}",
              "code": 2,
              "isclick": "1",
              "pid": 1175,
              "isDirectory": "1",
              "layertype": "map-image",
              "layerurl": "/csdnMap/jhgt/jhgt131/4cfe4df3e41c4912973927c2f4a626be/arcgis/rest/services/CS330700G2001_SQSX/GCS330700G2001_SQSX_CZKFBJ_2022/MapServer",
              "number": 0
            },
            {
              "id": 576,
              "category": "城镇规划",
              "name": "规划用地",
              "sub_category": "",
              "active": "1",
              "order_num": 7,
              "url": "/csdnMap/jhgt/jhgt/f99f826b5e764d87b083412057587bcb/arcgis/rest/services/GCS330700_2007_CSZTGH/GCS330700_2007_ZG_GHYD/MapServer/export?transparent=true&bbox=&SRS=EPSG:4490&STYLES=&WIDTH=256&HEIGHT=256&f=image&BBOX={bbox-epsg-4490}",
              "code": 2,
              "isclick": "1",
              "pid": 1175,
              "isDirectory": "1",
              "layertype": "map-image",
              "layerurl": "/csdnMap/jhgt/jhgt/f99f826b5e764d87b083412057587bcb/arcgis/rest/services/GCS330700_2007_CSZTGH/GCS330700_2007_ZG_GHYD/MapServer",
              "number": 0
            },
            {
              "id": 574,
              "category": "城镇规划",
              "name": "2019年规划用地",
              "sub_category": "",
              "active": "1",
              "order_num": 8,
              "url": "/csdnMap/jhgt/jhgt/3e6569bbaeff41999fd60f2b59ab2975/arcgis/rest/services/GCS330700_2007_CSZTGH/GCS330000_2007_CZZTGH_GHYD_2019/MapServer/export?transparent=true&bbox=&SRS=EPSG:4490&STYLES=&WIDTH=256&HEIGHT=256&f=image&BBOX={bbox-epsg-4490}",
              "code": 2,
              "isclick": "1",
              "pid": 1175,
              "isDirectory": "1",
              "layertype": "map-image",
              "layerurl": "/csdnMap/jhgt/jhgt/3e6569bbaeff41999fd60f2b59ab2975/arcgis/rest/services/GCS330700_2007_CSZTGH/GCS330000_2007_CZZTGH_GHYD_2019/MapServer",
              "number": 0
            },
            {
              "id": 589,
              "category": "城镇规划",
              "name": "全市域控制性详细规划（2022年更新）",
              "sub_category": "",
              "active": "1",
              "order_num": 10,
              "url": "/csdnMap/jhgt/jhgt130/3c7dd7ac562e4e11bf0d3cde219a9b8f/arcgis/rest/services/JH/qsykzxxxgh/MapServer/export?transparent=true&bbox=&SRS=EPSG:4490&STYLES=&WIDTH=256&HEIGHT=256&f=image&BBOX={bbox-epsg-4490}",
              "code": 2,
              "isclick": "1",
              "icon": "",
              "pid": 1175,
              "isDirectory": "1",
              "layertype": "map-image",
              "layerurl": "/csdnMap/jhgt/jhgt130/3c7dd7ac562e4e11bf0d3cde219a9b8f/arcgis/rest/services/JH/qsykzxxxgh/MapServer",
              "number": 0
            },
            {
              "id": 591,
              "category": "城镇规划",
              "name": "控规规划地块（季度更新）",
              "sub_category": "",
              "active": "1",
              "order_num": 10,
              "url": "/csdnMap/jhgt/jhgt/3823424770114e1083ac273d9b0660d4/arcgis/rest/services/GCS330700_2007_KZXXXGH/GCS330700_2007_KZXXXGH_GHDK_GX/MapServer/export?transparent=true&bbox=&SRS=EPSG:4490&STYLES=&WIDTH=256&HEIGHT=256&f=image&BBOX={bbox-epsg-4490}",
              "code": 2,
              "isclick": "1",
              "pid": 1175,
              "isDirectory": "1",
              "layertype": "map-image",
              "layerurl": "/csdnMap/jhgt/jhgt/3823424770114e1083ac273d9b0660d4/arcgis/rest/services/GCS330700_2007_KZXXXGH/GCS330700_2007_KZXXXGH_GHDK_GX/MapServer",
              "number": 0
            }
          ],
          "category": "城镇规划",
          "name": "城镇规划",
          "pid": 0,
          "number": 10,
        },
        {
          icon: '#icon-wenjianjia',
          children: [
            {
              "id": 1569,
              "category": "数字高程模型",
              "name": "等高线",
              "sub_category": "",
              "active": "1",
              "order_num": 0,
              "url": "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/JH_Contour200/MapServer",
              "code": 2,
              "isclick": "1",
              "pid": 1134,
              "isDirectory": "1",
              "layertype": "map-image",
              "layerurl": "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/JH_Contour200/MapServer",
              "number": 0
            },
          ],
          "category": "数字高程模型",
          "name": "数字高程模型",
          "pid": 0,
          "number": 1,
        },
        {
          icon: '#icon-wenjianjia',
          children: [
            {
              "id": 1000001,
              "category": "监测站",
              "name": "空气监测站（国控）",
              "sub_category": "tc_kq1",
              "active": "1",
              "order_num": 1,
              "url": "/csdnMap/jhyjzh-server/screen_api/tcgl/wxjk",
              "code": 6,
              "isclick": "1",
              "pid": 1157,
              "isDirectory": "1",
              "number": 0
            },
            {
              "id": 1000002,
              "category": "监测站",
              "name": "空气监测站（市控）",
              "sub_category": "tc_kq2",
              "active": "1",
              "order_num": 1,
              "url": "/csdnMap/jhyjzh-server/screen_api/tcgl/wxjk",
              "code": 6,
              "isclick": "1",
              "pid": 1157,
              "isDirectory": "1",
              "number": 0
            },
            {
              "id": 1000003,
              "category": "监测站",
              "name": "空气监测站（乡镇）",
              "sub_category": "tc_kq",
              "active": "1",
              "order_num": 1,
              "url": "/csdnMap/jhyjzh-server/screen_api/tcgl/wxjk",
              "code": 6,
              "isclick": "1",
              "pid": 1157,
              "isDirectory": "1",
              "number": 0
            },
            {
              "id": 1000004,
              "category": "监测站",
              "name": "地表水断面监测站（国控）",
              "sub_category": "tc_shj",
              "active": "1",
              "order_num": 1,
              "url": "/csdnMap/jhyjzh-server/screen_api/tcgl/wxjk",
              "code": 6,
              "isclick": "1",
              "pid": 1157,
              "isDirectory": "1",
              "number": 0
            },
            {
              "id": 1000005,
              "category": "监测站",
              "name": "地表水断面监测站（省控）",
              "sub_category": "tc_shj1",
              "active": "1",
              "order_num": 1,
              "url": "/csdnMap/jhyjzh-server/screen_api/tcgl/wxjk",
              "code": 6,
              "isclick": "1",
              "pid": 1157,
              "isDirectory": "1",
              "number": 0
            },
            {
              "id": 1000006,
              "category": "监测站",
              "name": "地表水断面监测站（市控）",
              "sub_category": "tc_shj2",
              "active": "1",
              "order_num": 1,
              "url": "/csdnMap/jhyjzh-server/screen_api/tcgl/wxjk",
              "code": 6,
              "isclick": "1",
              "pid": 1157,
              "isDirectory": "1",
              "number": 0
            },
            {
              "id": 1000007,
              "category": "监测站",
              "name": "地表水断面监测站（交接断面）",
              "sub_category": "tc_shj3",
              "active": "1",
              "order_num": 1,
              "url": "/csdnMap/jhyjzh-server/screen_api/tcgl/wxjk",
              "code": 6,
              "isclick": "1",
              "pid": 1157,
              "isDirectory": "1",
              "number": 0
            },
            {
              "id": 1000008,
              "category": "监测站",
              "name": "废气排放监测站",
              "sub_category": "tc_fq",
              "active": "1",
              "order_num": 1,
              "url": "/csdnMap/jhyjzh-server/screen_api/tcgl/wxjk",
              "code": 6,
              "isclick": "1",
              "pid": 1157,
              "isDirectory": "1",
              "number": 0
            },
            {
              "id": 1000009,
              "category": "监测站",
              "name": "废水排放监测站",
              "sub_category": "tc_fs",
              "active": "1",
              "order_num": 1,
              "url": "/csdnMap/jhyjzh-server/screen_api/tcgl/wxjk",
              "code": 6,
              "isclick": "1",
              "pid": 1157,
              "isDirectory": "1",
              "number": 0
            },
            {
              "id": 1000010,
              "category": "监测站",
              "name": "污染企业信息",
              "sub_category": "tc_wrqy",
              "active": "1",
              "order_num": 1,
              "url": "/csdnMap/jhyjzh-server/screen_api/tcgl/wxjk",
              "code": 6,
              "isclick": "1",
              "pid": 1157,
              "isDirectory": "1",
              "number": 0
            }
          ],
          "category": "监测站",
          "name": "监测站",
          "pid": 0,
          "number": 10,
        },
        {
          icon: '#icon-wenjianjia',
          children: [
            {
              "id": 14,
              "category": "政府机关",
              "name": "政府机关",
              "sub_category": "190101,190102,190103,190104,190105,190106,190107,190108,190109",
              "active": "1",
              "order_num": 13,
              "url": "https://yt.zjasm.net/apigw/services/arcgisdynamicmapservicelayer/dangzhengjiguan/latest/sy-6786f525-a9bd-477f-ab4a-f26d29524077/export?transparent=true&bbox=&SRS=EPSG:4490&STYLES=&WIDTH=256&HEIGHT=256&f=image&BBOX={bbox-epsg-4490}",
              "code": 1,
              "isclick": "1",
              "icon": "rckz-政府机关",
              "pid": 830,
              "isDirectory": "1",
              "layertype": "map-image",
              "layerurl": "https://yt.zjasm.net/apigw/services/arcgisdynamicmapservicelayer/dangzhengjiguan/latest/sy-6786f525-a9bd-477f-ab4a-f26d29524077",
              "number": 0
            },
          ],
          "category": "机关",
          "name": "机关",
          "pid": 0,
          "number": 1,
        },
        {
          icon: '#icon-wenjianjia',
          children: [
            {
              "id": 1000011,
              "category": "气象专题",
              "name": "气象专题_降雨",
              "sub_category": "",
              "active": "1",
              "order_num": 0,
              "url": "/csdnMap/gismap/egis-map-engine/services/js/achievement/achievement_grid_tile_133/hexadecimal/{z}/{x}/{y}.png?token=3adffb2d586245e182c24828c8b63989",
              "code": 2,
              "isclick": "1",
              "pid": 1134,
              "isDirectory": "1",
              "layertype": "map-image",
              "layerurl": "/csdnMap/gismap/egis-map-engine/services/js/achievement/achievement_grid_tile_133/hexadecimal/{z}/{x}/{y}.png?token=3adffb2d586245e182c24828c8b63989",
              "number": 0
            },
            {
              "id": 1000012,
              "category": "气象专题",
              "name": "气象专题_相对湿度",
              "sub_category": "",
              "active": "1",
              "order_num": 0,
              "url": "/csdnMap/gismap/egis-map-engine/services/jh_shidu1107/achievement/achievement_grid_tile_138/hexadecimal/{z}/{x}/{y}.png?token=3adffb2d586245e182c24828c8b63989",
              "code": 2,
              "isclick": "1",
              "pid": 1134,
              "isDirectory": "1",
              "layertype": "map-image",
              "layerurl": "/csdnMap/gismap/egis-map-engine/services/jh_shidu1107/achievement/achievement_grid_tile_138/hexadecimal/{z}/{x}/{y}.png?token=3adffb2d586245e182c24828c8b63989",
              "number": 0
            },
            {
              "id": 1000013,
              "category": "气象专题",
              "name": "气象专题_云量",
              "sub_category": "",
              "active": "1",
              "order_num": 0,
              "url": "/csdnMap/gismap/egis-map-engine/services/jh_yunliang1107/achievement/achievement_grid_tile_137/hexadecimal/{z}/{x}/{y}.png?token=3adffb2d586245e182c24828c8b63989",
              "code": 2,
              "isclick": "1",
              "pid": 1134,
              "isDirectory": "1",
              "layertype": "map-image",
              "layerurl": "/csdnMap/gismap/egis-map-engine/services/jh_yunliang1107/achievement/achievement_grid_tile_137/hexadecimal/{z}/{x}/{y}.png?token=3adffb2d586245e182c24828c8b63989",
              "number": 0
            },
            {
              "id": 1000014,
              "category": "气象专题",
              "name": "气象专题_风速",
              "sub_category": "",
              "active": "1",
              "order_num": 0,
              "url": "/csdnMap/gismap/egis-map-engine/services/jh_fengsu1107/achievement/achievement_grid_tile_135/hexadecimal/{z}/{x}/{y}.png?token=3adffb2d586245e182c24828c8b63989",
              "code": 2,
              "isclick": "1",
              "pid": 1134,
              "isDirectory": "1",
              "layertype": "map-image",
              "layerurl": "/csdnMap/gismap/egis-map-engine/services/jh_fengsu1107/achievement/achievement_grid_tile_135/hexadecimal/{z}/{x}/{y}.png?token=3adffb2d586245e182c24828c8b63989",
              "number": 0
            },
          ],
          "category": "气象专题",
          "name": "气象专题",
          "pid": 0,
          "number": 5,
        },
      ],
      treeArr: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      showDialog:false,
      dialogTitle:"",
      PointObjList: [],
      mapData:mapData,
      kfqData:kfqData
    }
  },
  components: {
    Commondialog,
    CommonDialogDetail2
  },
  computed: {},
  mounted() {

  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    // 取消搜索内容
    clearSearchValue() {
      // 收起图层,显示所有图层
      this.expandAll = false
      this.searchValue = ''
      this.searchData()
      mapUtil.searchTool.clear()
      this.expandNodes(this.$refs.tree.store.root)
    },
    // 搜索按钮点击
    searchData() {
      this.searchValue = this.searchValue.trim()
      this.$refs.tree.filter(this.searchValue)
      this.expandAll = true
      this.expandNodes(this.$refs.tree.store.root)
    },
    // 清除按钮点击
    qxALLfun() {
      this.$refs.tree.setCheckedNodes([])
      this.expandAll = false
      this.searchValue = ''
      this.$refs.tree.filter(this.searchValue)
      this.expandNodes(this.$refs.tree.store.root)
      mapUtil.searchTool.clear()
    },
    // 复选框选择
    clickChange(nodes, flag) {
      this.treeArr = this.$refs.tree.getCheckedNodes()
      nodes.flag = flag

      //构建开发区两个面的面数据
      const kfqPolygon1 = new ArcGisUtils.Polygon({
        hasZ: false,
        hasM: false,
        rings: this.kfqData.data.features[0].geometry.coordinates[1],
        spatialReference: { wkid: 4326 }
      });
      const kfqPolygon2 = new ArcGisUtils.Polygon({
        hasZ: false,
        hasM: false,
        rings: this.kfqData.data.features[0].geometry.coordinates[2],
        spatialReference: { wkid: 4326 }
      });
      const kfqGraphic1=new ArcGisUtils.Graphic({
        geometry: kfqPolygon1
      })
      const kfqGraphic2=new ArcGisUtils.Graphic({
        geometry: kfqPolygon2
      })
      let wktStr1 = ArcGisUtils.arcgisToWkt(kfqGraphic1.geometry.clone());
      let wktStr2 = ArcGisUtils.arcgisToWkt(kfqGraphic2.geometry.clone());
      if (nodes.flag) {
        switch (Number(nodes.code)) {
          case 1:
            //查询开发区两个面的poi数据
            this.Synchronization(nodes,wktStr1,wktStr2).then(res => {
              let pointData = []
              let textData = []
              res.forEach((item) => {
                let address = item.address || item.city + item.county + item.town
                let str = {
                  data: {
                    item: item,
                    key: ['名称', '类型', '地址'],
                    value: [item.name, item.shortname, address],
                  },
                  id: nodes.id,
                  pointId: 'tcgl' + nodes.label,
                  lng: item.x,
                  lat: item.y,
                }
                let textStr = {
                  pos: [item.x, item.y, 0],
                  text: item.name,
                }
                if (
                  item.x != '' &&
                  item.x != null &&
                  item.x != undefined &&
                  item.x != 'null' &&
                  item.x != 'undefined'
                ) {
                  pointData.push(str)
                  textData.push(textStr)
                }
              })
              let pointId = 'tcgl' + nodes.name
              this.pointTextMapFun(nodes.icon, pointData, pointId, textData, nodes.label, 1)
            })
            break;
          case 2:
            this.loadLayer(nodes)
            break;
          case 5:
            //走大脑指标接口
            getCsdnInterface(nodes.url,{code: nodes.sub_category}).then((res) => {
              let pointData = []
              let textData = []
              let pointDataId = [684,265,266,267,268,269,270,272,274].includes(nodes.id)?'szwh':''
              let pointIconSize = 0.8
              res.data.data.forEach((item, index) => {
                let pointArr = item.lng?item.lng.split(','):[item.longitude,item.latitude]
                let str = {
                  data: item,
                  id: nodes.id,
                  choiceId: pointDataId,
                  pointId: 'tcgl' + nodes.name,
                  lng: pointArr[0],
                  lat: pointArr[1],
                }
                let textStr = {
                  pos: [pointArr[0], pointArr[1], 0],
                  text: item.ly,
                }
                if (
                  item.lng != '' &&
                  item.lng != 0.0 &&
                  item.lng != null &&
                  item.lng != undefined &&
                  item.lng.indexOf('无') == -1 &&
                  item.lng != '0,0' &&
                  item.lng != 'null' &&
                  item.lng != 'undefined' ||
                  item.longitude && item.latitude
                ) {
                  pointData.push(str)
                  textData.push(textStr)
                }
              })
              let pointId = 'tcgl' + nodes.name
              this.pointTextMapFun(nodes.icon, pointData, pointId, textData, nodes.name, pointIconSize)
            })
            break;
          case 6:
            //侍作磊接口
            axios({
              method: 'get',
              url: nodes.url,
              params: {
                type: nodes.sub_category
              }
            }).then((res) => {
              this.addPointMap(res.data.data[0], nodes)
            })
            break;
          case 7:
            axios({
              method: 'get',
              url: nodes.url
            }).then((res) => {
              this.addPointMap(res.data.data, nodes)
            })
            break;
          case 8:
            this.shapeFun(nodes.name)
            break;
        }
      } else {
        this.rmMapFun("tcgl" + nodes.name)
      }
    },
    async Synchronization(nodes,wktStr1,wktStr2) {
      const res1 = await this.solrSearch(nodes,wktStr1)
      const res2 = await this.solrSearch(nodes,wktStr2)
      return res1.data.data.concat(res2.data.data)
    },
    async solrSearch(nodes, wktStr) {
      // 封装请求配置
      const requestConfig = layerPoiSearch({
        pageInfo: {
          current: 1,
          size: 5000, // 考虑动态设置，这里保持不变
        },
        text: nodes.sub_category,
        geoType: "wkt",
        tableNames: "poi",
        geometry: wktStr,
      })

      try {
        const response = await requestConfig;
        // 状态码检查
        if (response.status !== 200) {
          throw new Error('Request failed with status ' + response.status);
        }
        return response; // 返回数据而不是整个响应对象
      } catch (error) {
        console.error('Solr search error:', error);
        throw error; // 重新抛出错误，以便调用者可以处理
      }
    },
    // el-tree展开还是收起
    expandNodes(node) {
      node.expanded = this.expandAll
      for (let i = 0; i < node.childNodes.length; i++) {
        node.childNodes[i].expanded = this.expandAll
        if (node.childNodes[i].childNodes.length > 0) {
          this.expandNodes(node.childNodes[i])
        }
      }
    },
    //加载图层服务
    loadLayer(node) {
      top.mapUtil.loadTileLayer({
        type: node.layertype,
        layerid: "tcgl" + node.name,
        url: node.layerurl?node.layerurl:node.url,
        id: String(node.id),
        sublayers: node.sublayers?[{name:node.sublayers}]:null
      })
      if (node.id == 1559 || node.id == 1601 || node.id == 1745) {
        //金华之心视角
        top.mapUtil.flyTo({
          x:"119.6335222183267",
          y:"29.07679284135244",
          z:"166.24769043549895",
          heading:"8.430731081264618",
          tilt:"75.93647640921957"
        })
      } else if (node.id == 1255) {
        //市政府视角
        top.mapUtil.flyTo({
          "x": 119.64416179491072,
          "y": 29.074682008965627,
          "z": 418.1874582050368,
          "heading": 349.4264001508748,
          "tilt": 65.4171830290876
        })
      }
    },
    // 清除地图
    rmMapFun(id) {
      mapUtil.removeLayer(id)
    },
    //添加点位 重点火灾
    async addPointMap (data, nodes) {
      let img = nodes.icon || data.pointType || 'sjzx-其他'
      let point = []
      const poly = turf.polygon(this.mapData.data.features.find(a => a.properties.name == '金华市').geometry.coordinates[0])
      data.pointData.map(a => {
        if (turf.booleanPointInPolygon(turf.point([a.lng, a.lat]), poly)) {
          point.push(a)
        }
      })
      this.pointTextMapFun(img,point,'tcgl' + nodes.name)
    },
    // 加载3D文字和地图点位的方法
    pointTextMapFun(icon, pointData, pointId, textData, textId, iconSize) {
      let img = icon || 'sjzx-其他'
      top.mapUtil.loadPointLayer({
        data: pointData,
        layerid: pointId, //图层id
        iconcfg: {image: img, iconSize: iconSize?iconSize:0.4}, //图标
        onclick: this.openPointMassage,
        popcfg: {
          offset: [50, -100],
          show: false,
        },
      })
    },
    // 查看地图点位点击的详情
    openPointMassage(e, list) {
      console.log(e, '点位详情')
      console.log(list, '聚合列表')
      if (e.data) {
        if (e.data.key != undefined && e.data.key != '') {
          let coor = [e.lng, e.lat]
          this.showMapPop(coor, e.data, e.layerid)
        }
      }
      if (e.data.device_name && e.id != undefined) {
        this.showMapPop([e.data.longitude,e.data.latitude], {key:['名称：'],value:[e.data.device_name]}, e.layerid)
      }
      if (e.choiceId == 'szwh') {
        let coor = e.data.lng?e.data.lng.split(','):[e.data.longitude,e.data.latitude]
        this.getWhcgDetail(coor, e.data, e.pointId)
      }
    },
    //点位弹窗
    showMapPop (coor, data, id) {
      let arr = data.key.map((a, i) => {
        return { name: a, value: data.value[i] }
      })
      this.PointObjList = arr;
      this.dialogTitle = "点位详情";
      this.showDialog = true;
    },
    //获取文化场馆详细信息
    getWhcgDetail(coor, obj, id) {
      //大脑指标接口
      getCsdnInterface("yxzl_szwh_center012",{id: obj.id, code: obj.ly,}).then(res => {
        let arr = {
          key: Object.keys(res.data.data[0]),
          value: Object.values(res.data.data[0]),
        }
        this.showMapPop(coor, arr, id)
      })
    },
    // 网格上图
    shapeFun (name, allName) {
      let label = name.split('_')[1]
      top.mapUtil.findTask({
        type: 'qxgrid',
        key: label,
        callback: (res) => {
          top.mapUtil.loadPolygonLayer({
            layerid: 'tcgl' + name,
            data: res,
            style: {
              strokeColor: [255, 50, 40, 0.9], //多边形轮廓颜色透明度
              fillColor: [193, 210, 240, 0.2], //多边形填充色
            },
            zoomToLayer: false
          })
        },
      })
    },
  },
  watch: {
    searchValue(val) {
      this.$refs.tree.filter(val);
    }
  }
}
</script>

<style scoped lang="less">
  .panel-Container {
    width: 791px;
    height: 932px;
    background: url("~@/assets/common/bg.png") no-repeat;
    background-size: cover;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    .icon {
      width: 38px;
      height: 31px;
      vertical-align: -0.15em;
      fill: currentColor;
      overflow: hidden;
    }
    .point-no {
      color: #c1c1c1f2;
      /* pointer-events: none;
          cursor: default; */
      cursor: not-allowed !important;
    }
    .open-file {
      background-image: url('~@/assets/map/open-file.png');
      background-size: 100% 100%;
    }
    .close-file {
      background-image: url('~@/assets/map/close-file.png') !important;
      background-size: 100% 100%;
    }
    .s-m-l-10 {
      margin-left: 10px !important;
    }
    .panel-Container-inner {
      width: 700px;
      height: fit-content;
      margin-top: 30px;
      .panel-Container-text {
        width: 350px;
        height: fit-content;
        box-sizing: border-box;
        font-size: 40px;
        color: rgb(255, 255, 255);
        display: flex;
        margin-top: 10px;
      }
      /* 搜索框 */
      .panel-Container-searchLine {
        width: 100%;
        height: 70px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 30px;
        .sou-css {
          width: 130px;
          height: 70px;
          font-size: 30px;
          line-height: 70px;
          text-align: center;
          color: #fff;
          border-radius: 10px;
          margin-left: 20px;
          background: url('~@/assets/common/clear-bg.png') no-repeat;
          background-size: 100% 100%;
          cursor: pointer;
        }
        /deep/ .el-input-group {
          width: 100%;
          height: 100%;
          background: url('~@/assets/common/search_bg.png') no-repeat;
          background-size: 100% 100%;
          overflow: hidden;
        }
        /deep/ .el-input-group__append {
          width: 20px;
          border-color: transparent;
          background-color: transparent;
          border-radius: 0;
        }
        /deep/ .el-input__inner {
          border-radius: 0;
          border: transparent;
          background: transparent;
          font-size: 30px;
        }
        /deep/ .el-input.is-active {
          .el-input__inner, .el-input__inner:focus {
            border-color: transparent;
          }
        }
        button:hover {
          color: #fff !important;
        }
        button {
          color: #afdcfb !important;
          font-size: 1.5rem !important;
          height: 3.125rem !important;
          border-radius: 0 !important;
          border-color: transparent !important;
        }
      }
      .tree-container {
        /deep/ .el-tree {
          background-color: transparent;
          color: white;
          margin-top: 30px;
          height: 650px;
          overflow-y: scroll;
          .el-tree-node__children {
            margin-left: 25px;
          }
          .el-tree-node__content:hover, .el-tree-node:focus>.el-tree-node__content {
            background: linear-gradient(0deg, #00f6ff 0%, #00a2ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .el-tree-node__content {
            height: 80px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .el-tree-node__label {
              font-size: 35px;
              margin-left: 10px;
            }
          }
          .el-tree-node__expand-icon {
            display: none;
            font-size: 1.375rem;
          }
          .el-tree-node {
            .is-leaf+.el-checkbox .el-checkbox__inner {
              display: inline-block;
              width: 30px;
              height: 30px;
              margin-top: 5px;
            }
            .el-checkbox .el-checkbox__inner {
              display: none;
            }
            .el-checkbox .el-checkbox__inner::after {
              height: 11px;
              left: 10px;
              top: 3px;
            }
          }
          .el-tree__empty-text {
            font-size: 24px;
            color: rgba(255, 255, 255, 0.614);
          }
        }
      }
    }
  }
</style>