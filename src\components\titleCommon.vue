<template>
  <div class="title">
    <div class="titleLine-1" :style="{width:width+'px'}">
      <div class="lefticon"></div>
      <div class="name" @click="titleClick">{{titleText}}</div>
      <div class="line"></div>
      <slot></slot>
      <div class="date" v-show="showDate">数据至: 2023年4月</div>
      <div class="fkClass"></div>
      <div class="fkClass"></div>
      <div class="fkClass"></div>
    </div>
    <div class="titleLine-2"></div>
  </div>
</template>

<script>
export default {
  data () {
    return {}
  },
  props: {
    titleText: {
      type: String,
      default: ""
    },
    width:{
      type:String,
      default:"1937"
    },
    showDate: {
      type: Boolean,
      default: true
    }
  },
  computed: {},
  mounted () {

  },
  methods: {
    titleClick () {
      this.$emit('titleClick')
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
.title {
  width: 100%;

  .titleLine-1 {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    height: 51px;
    .lefticon {
      width: 70px;
      height: 50px;
      background: url('../assets/common/titleleft.png') no-repeat;
      background-size: cover;
    }
    .name {
      cursor: pointer;
      font-size: 54px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      background: linear-gradient(0deg, #00c0ff 0%, #caffff 50%, #ffffff 0%, #caffff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-left: 16px;
    }
    .line {
      flex: 1;
      height: 15px;
      background: #356596;
      opacity: 0.3;
      margin: 0 18px 0 24px;
    }
    .date {
      font-size: 26px;
      font-family: Source Han Sans SC;
      font-weight: 400;
      color: #d6e7f9;

      background: linear-gradient(180deg, #caffff 0%, #83b8ff 0%, #ffffff 0%, #caffff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .fkClass {
      width: 15px;
      height: 15px;
      margin-left: 15px;
      border: 1px solid #055e85;
    }
  }
  .titleLine-2 {
    width: 100%;
    height: 38px;
    background: url('~@/assets/common/titlebase.png') no-repeat center center;
    background-size: 100% 100%;
  }
}
</style>