<template>
  <div class="title">
    <div class="titleLine-1" :style="{width:width+'px'}">
        <div class="lefticon"></div>
        <div class="name" @click="titleClick">{{titleText}}</div>
        <div class="line"></div>
        <slot></slot>
        <!-- <div class="date">数据至: {{ date }}</div> -->
    </div>
    <div class="titleLine-2" :style="{width:width+'px'}"></div>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  props:{
    titleText:{
      type:String,
      default:""
    },
    width:{
      type:String,
      default:"1937"
    },
    date:{
      type:String,
      default:"2023年4月"
    }
  },
  computed: {},
  mounted() {

  },
  methods: {
    titleClick() {
      this.$emit('titleClick')
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .title {
    .titleLine-1 {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      width: 1937px;
      height: 51px;
      .lefticon {
        width: 70px;
        height: 50px;
        background: url("../assets/common/titleleft.png") no-repeat;
        background-size: cover;
      }
      .name {
        cursor: pointer;
        font-size: 54px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #FFFFFF;
        // text-shadow: 0px 4px 9px rgba(0,0,0,0.29);
        background: linear-gradient(0deg, #CAFFFF 0%, #CAFFFF 0%, #FFFFFF 0%, #00C0FF 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-left: 16px;
      }
      .line {
        flex: 1;
        height: 15px;
        // background: #356596;
        background:repeating-linear-gradient(to right,#356596,#082D4E);
        opacity: 0.3;
        margin: 0 18px 0 24px;
      }
      .date {
        font-size: 26px;
        font-family: Source Han Sans SC;
        font-weight: 400;
        color: #D6E7F9;
        line-height: 25px;
        // text-shadow: 0px 4px 9px rgba(0,0,0,0.29);

        background: linear-gradient(180deg, #CAFFFF 0%, #CAFFFF 0%, #FFFFFF 0%, #83B8FF 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
    .titleLine-2 {
      width: 1937px;
      height: 38px;
      background: url("../assets/common/titlebase.png") no-repeat;
      background-size: cover;
    }
  }
</style>