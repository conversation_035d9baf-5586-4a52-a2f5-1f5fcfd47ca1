<!--仪表盘-->
<template>
  <div>
      <div class="dash" :id="'dashCharts' + id"></div>
  </div>
</template>

<script>
export default {
  name: "Dashboard",
  data() {
    return {

    }
  },
  props:{
    id:{
      type:String,
      default:""
    },
    value:{
      type:Number,
      default:0
    }
  },
  computed: {},
  mounted() {
    this.initCharts()
  },
  methods: {
    initCharts() {
      var axisTickLength = 10 //刻度线宽度
      var value = this.value
      var colorRegionRate = (value / 100).toFixed(2)

      var startColor = 'rgba(0,192,255,0.4)'
      var endColor = '#00C0FF'

      let myChart = this.$echarts.init(document.getElementById("dashCharts"+ this.id));
      let option = {
        series: [
          {
            type: 'gauge',
            name: '第二层',
            radius: '100%',
            startAngle: '225',
            endAngle: '-45',
            splitNumber: 4,
            pointer: {
              show: true,
              length: '53%'
            },
            // 仪表盘指针样式。
            itemStyle: {
              color: '#f6fefe'
            },
            data: [{
              value: value,
              name: ''
            }],
            title: {
              show: false,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: [
                  // 有数值的部分
                  [colorRegionRate, new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                    offset: 0,
                    color: startColor // 0% 处的颜色
                  },
                    {
                      offset: 1,
                      color: endColor // 100% 处的颜色
                    }
                  ])],
                  // 底色
                  [1, new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                    offset: 0,
                    color: 'rgba(255,255,255,0.1)' // 0% 处的颜色
                  },
                    {
                      offset: 1,
                      color: 'rgba(255,255,255,0.1)' // 100% 处的颜色
                    }
                  ])]
                ],
                width: 30,
                shadowOffsetX: 0,
                shadowOffsetY: 0,
                opacity: 1
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
            detail: {
              show: false
            },
            axisLabel: {
              show: false,
            },
            animationDuration: 2000,
          },
          {
            name: '第三层渐变圈',
            type: 'gauge',
            radius: '20%',
            startAngle: '225',
            endAngle: '-45',
            splitNumber: 4,
            pointer: {
              show: false
            },
            data: [{
              value: value,
              name: ''
            }],

            axisLine: {
              show: true,
              lineStyle: {
                color: [
                  // 有数值的部分
                  [colorRegionRate, new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                    offset: 0,
                    color: startColor // 0% 处的颜色
                  },
                    {
                      offset: 1,
                      color: endColor // 100% 处的颜色
                    }
                  ])],
                  // 底色
                  [1, new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                    offset: 0,
                    color: 'rgba(255,255,255,0.1)' // 0% 处的颜色
                  },
                    {
                      offset: 1,
                      color: 'rgba(255,255,255,0.1)' // 100% 处的颜色
                    }
                  ])]
                ],
                width: axisTickLength, // 刻度线宽度
                shadowOffsetX: 0,
                shadowOffsetY: 0,
                opacity: 1
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false,
            },
            // 仪表盘指针样式。
            itemStyle: {
              color: '#f6fefe'
            },
            title: {
              show: false,
            },
            detail: {
              show: false
            },
            axisLabel: {
              show: false,
            },
            animationDuration: 2000,
          },
          {
            name: '与div背景色相同模拟axisTick',
            type: 'gauge',
            radius: '90%',
            startAngle: '225',
            endAngle: '-45',
            splitNumber: 1,
            pointer: {
              show: false
            },
            title: {
              show: false,
            },
            detail: {
              show: true,
              offsetCenter: [0, '80%'],
              formatter: (value) => {
                return [
                  `{a|${value}}{b|%}`,
                ].join('\n')
              },
              rich: {
                a: {
                  color: '#00C0FF',
                  fontSize: 36,
                  fontFamily: 'BN',
                },
                b: {
                  color: '#00C0FF',
                  fontSize: 26,
                  fontFamily: 'BN',
                },
                c: {
                  color: '#00C0FF',
                  fontSize: 26,
                  fontFamily: 'BN',
                },
              }
            },
            data: [{
              value: value,
              name: ''
            }],
            axisLine: {
              show: false,
              lineStyle: {
                width: 1,
                opacity: 0
              }
            },
            axisTick: {
              show: true,
              splitNumber: 120,
              length: axisTickLength, // 刻度线宽度
              lineStyle: {
                // 与div背景色相同
                color: '#353c60',
                width: 5,
              }
            },
            splitLine: {
              show: false,
            },
            axisLabel: {
              show: false
            }
          },
        ]
      };
      myChart.setOption(option)
      myChart.getZr().on('mousemove', param => {
        myChart.getZr().setCursorStyle('default')
      })
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .dash {
    width: 167px;
    height: 167px;
  }
</style>