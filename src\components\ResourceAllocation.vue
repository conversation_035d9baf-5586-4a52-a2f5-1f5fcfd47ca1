<template>
  <div class="container">
    <el-checkbox-group v-model="checkList" @change="change">
      <el-checkbox :label="item" v-for="(item,i) in list" :checked="item.checked" :key="i"><div :title="item[labelKey]" class="label">{{item[labelKey]}}</div></el-checkbox>
    </el-checkbox-group>
  </div>
</template>

<script>
export default {
  name: "ResourceAllocation",
  props: {
    list: {
      type: Array,
      default() {
        return []
      }
    },
    labelKey: {
      type: String,
      default() {
        return 'label'
      }
    },
    //最大勾选个数
    maxCheckSize: {
      type: Number,
      default: 5
    }
  },
  data() {
    return {
      checkList: []
    }
  },
  computed: {},
  mounted() {
    this.change()
  },
  methods: {
    change() {
      // 如果勾选的项目数量超过了允许的最大值
      if (this.checkList.length > this.maxCheckSize) {
        // 取消最后一个被勾选的项目（简单处理，实际场景可能需要更复杂的逻辑来决定取消哪个）
        const lastChecked = this.checkList.pop();
        this.$message.warning(`最多只能选择 ${this.maxCheckSize} 项，已取消选择 "${lastChecked[this.labelKey]}"`);
      }

      // 发送更新的勾选列表到父组件
      this.$emit('change', this.checkList);
    },
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .container {
    width: 280px;
    height: 391px;
    background: url("~@/assets/common/sourceBg.png") no-repeat;
    background-size: cover;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    /deep/ .el-checkbox-group {
      width: 232px;
      height: 320px;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      flex-direction: column;
      overflow-y: scroll;
      .el-checkbox {
        margin-bottom: 9px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .el-checkbox__label {
          .label {
            width: 150px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-family: Source Han Sans, Source Han Sans;
            font-weight: 400;
            font-size: 28px;
            color: #FFFFFF;
            line-height: 40px;
            text-align: left;
            font-style: normal;
          }
        }
        .el-checkbox__inner {
          width: 26px;
          height: 26px;
          background: url("~@/assets/common/check.png") no-repeat;
          background-size: cover;
          border: unset;
        }
        .el-checkbox__inner::after {
          left: 12px;
          top: 8px;
        }
      }
    }
  }
</style>