<template>
  <div class="container">
    <div class="table" :style="{width: width + 'px',height: (height+58) + 'px' }">
      <div class="table-title">
        <div class="table-cell" v-for="(item,i) in titleWithKey" :key="i" :style="{flex:item.flex?item.flex:1}"> <span style="margin-left: 20px;">{{item.label}}</span></div>
      </div>
      <div class="table-container">
        <div class="table-row" v-for="(item,i) in tableData" :key="i">
          <div class="table-cell" v-for="(obj,j) in titleWithKey" :key="j" :style="{flex:obj.flex?obj.flex:1}">
            <div class="cell-inner" :title="item[titleWithKey[j].key]?item[titleWithKey[j].key]:'-'">
              {{item[titleWithKey[j].key]?item[titleWithKey[j].key]:"-"}}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "index",
  props: {
    tableData: {
      type: Array,
      default: () => {
        return []
      }
    },
    titleWithKey: {
      type: Array,
      default: () => {
        return []
      }
    },
    width: {
      type: Number,
      default: 862
    },
    height: {
      type: Number,
      default: 329
    }
  },
  data() {
    return {

    }
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .table {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      margin: 30px 0 30px 0;
      .table-cell {
        flex: 1;
        height: 100%;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 28px;
        color: #FFFFFF;
        text-align: left;
        overflow: hidden;
        text-overflow: ellipsis;
        .cell-inner {
          text-align: left;
          margin: 14px 0 13px 23px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      .table-title {
        width: 100%;
        height: 58px !important;
        background: transparent;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 32px;
        color: #E4EBF4;
        line-height: 58px;
        text-align: left;
        font-style: normal;
      }
      .table-container {
        width: 100%;
        height: 100%;
        overflow-y: scroll;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        .table-row {
          width: 100%;
          height: 58px;
          background: rgba(0,0,0,0.2);
          display: flex;
          justify-content: flex-start;
          align-items: center;
          margin-top: 16px;

          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 28px;
          color: #E4EBF4;
          line-height: 36px;
          text-align: left;
          font-style: normal;
        }
      }
    }
  }
</style>