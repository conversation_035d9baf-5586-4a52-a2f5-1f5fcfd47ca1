import Draw from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/Draw.js";
import initSceneView from "./initSceneView.js";
import initMapView from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/initMapView.js";
import {
  loadArcgisLayer,
  removeViewLimit,
  addLimitToView,
} from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/loadArcgisLayer.js";
// import loadSceneLayer from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/loadSceneLayer.js";
import { zoomIn, zoomOut } from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/viewUtils.js";
import createWeatherWidget from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/createWeatherWidget.js";
import {
  createAreaMeasurementWidget,
  createAreaMeasurement,
} from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/createAreaMeasurementWidget.js";
import {
  createSiceWidget,
  createSliceAnalysis,
} from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/createSiceWidget.js";
import {
  measuringDistance,
  createmMeasuringDistance,
} from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/measuringDistance.js";
import {
  converPathToPoints,
  geodesic4490Areas,
} from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/geometryUtils.js";
import { queryTaskByGeometry } from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/queryUtils.js";
import {
  exchangeMap,
  addDEMToMap,
  removeDEMFromMap,
  changePoiLayerVisible,
} from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/exchangeMap.js";
import {
  loadWeatherLayer,
  removeWeatherLayer,
} from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/loadWeatherLayer.js";
import {
  addHeatMap,
  removeHeatmap,
  addLineEffect,
  removeLineEffect,
  addDynamicWall,
  removeDynamicWall,
  addScanRadar,
  removeScanRadar,
  addFireEffect,
  removeFireEffect,
  addFireSmokeEffect,
  removeFireSmokeEffect,
  addFountainEffect,
  removeFountainEffect,
  addCylinderLayer,
  removeCylinderLayer,
  addBarLayer,
  removeBarLayer,
  addPyramidLayer,
  removePyramidLayer,
  addRadarMask,
  removeRadarMask,
  addRouteLayer,
  removeRouteLayer,
  addMixHeatMap,
  removeMixHeatMap,
  addheatMap2,
  removeHeatMap2,
  addMix3dHeatmap,
  removeMix3dHeatmap,
  addPathsLineEffect,
  removePathsLineEffect,
} from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/effect/index.js";
import Swipe from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/swipe/swipe.js";
import RouteLayer from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/RouteLayer.js";
import Multiscreen from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/multiscreen/multiscreen.js";
import { addRoadLayer, removeRoadLayer } from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/loadRoadLayer.js";
import Skyline from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/skyline/index.js";
import Buffer from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/bufferAnalysis.js";
import {
  addWaterLayer,
  changeWaterLayer,
  removeWaterLayer,
  RainfallAnalysis,
} from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/loadWaterLayer.js";
import {
  addIntegratedMesh,
  removeIntegratedMesh,
} from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/loadIntegratedMesh.js";
import MapPopupWidget from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/MapPopupWidget.js";
import { setXYPos } from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/setPos.js";
import {
  addWhiteModalLayer,
  removeWhiteModalLayer,
  addGRADIENTBAIMOLayer,
  removeGRADIENTBAIMOLayer,
  addClassBreakModalLayer,
  removeClassBreakModalLayer,
} from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/whiteModalLayer.js";
import {
  isGeometryContains,
  isGeometryTouch,
  isGeometryDisjointed,
  wktToArcGis,
  arcgisToWkt,
  isGeometryIntersect,
  createBuffer,
} from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/geometryEngineUtils.js";
import { addVideoToMap, removeVideosFromMap } from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/videoToMap.js";
import {
  loadPureModalLayer,
  removePureModalLayer,
} from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/pureModalLayer.js";
import { setCam, updateCam } from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/setCam.js";
import MapClickEventHandle from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/MapClickEventHandle.js";
import { addBimModalLayer, removeBimModalLayer } from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/loadBimLayer.js";
import DomAnalysis from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/domAnalysis.js";
import esriConfig from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.26/@arcgis/core/config.js";
import { getTDTCGCS2000SYLayer, getSzLightBasemap } from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/basemap.js";
import addGeojsonToMap from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/addGeojsonToMap.js";
import initializationView from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/initializationView.js";
import { load3DTextLayer, remove3DTextById } from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/load3DTextLayer.js";
import addWaterWaveEffect from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/waterWaveEffect.js";
import { addGifLayer, removeGifLayer } from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/loadGifLayer.js";
import createLineOfSightWidget from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/createLineOfSightWidget.js";
import setGroundOpacity from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/setGroundOpacity.js";
import { ScatterToMap } from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/ScatterToMap.js";
import { TrackLayer } from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/TrackLayer.js";
import addLineLayer from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/addLineLayer.js";
import addArrowLineLayer from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/addArrowLineLayer.js";
import change2DOr3D from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/change2DOr3D.js";
import { createBuildingExplorer } from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/BuildingExplorer.js";
import {
  addExtrudeLayer,
  removeExtrudeLayer,
} from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/addExtrudeLayer.js";
import {
  addRoadVideoLayer,
  removeRoadVideoLayer,
} from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/addRoadVideoLayer.js";
import loadHeatmapLayer, {
  addJinHuaCoverLayer,
} from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/loadHeatmapLayer.js";
import createDaylightWidget from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/createDaylightWidget.js";
import loadFlyLineLayer from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/loadFlyLineLayer.js";
import projectPoint from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/projectPoint.js";
import ClusterLayer from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/esriClusterLayer.js";
import { addOdLineEffect, removeOdLineEffect } from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/effect/index.js";
import { setLayerVisible } from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/layerVisible.js";
import LayerFlicker, {
  pauseRoadFlicker,
  startRoadFlicker,
} from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/layerFlicker.js";
import {
  addTiandituRoutePlanLayer,
  removeTiandituRoutePlanLayer,
} from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/tiandituRoutePlanLayer.js";
import {
  addTiandituRoutePlanLayer2,
  removeTiandituRoutePlanLayer2,
  queryLocationByAddress,
} from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/test.js";

import PolygonBoolen from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/polygonBoolen.js";
import VisualTracking from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/visualTracking.js";
import RoamByHeading from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/roamByHeading.js";
import MeshCreate from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/meshCreate.js";
import Fly from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/fly.js";
import sketchExtrude from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/sketchExtrude.js";
import gltfModelToMap from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/addgltfModelToMap.js";
import addHexGridLayer from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/hexGridLayer/addHexGridLayer.js";
import addGridLayer from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/hexGridLayer/addGridLayer.js";
import addDefaultCustomLayer from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/addDefaultCustomLayer.js";
import { queryVector, removeCodeLayer } from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/queryVector.js";
import SpatialAnalysis from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/spatialAnalysis.js";
import AngleMeasure from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/angleMeasure.js";
import { customHeatmap } from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/effect/heatmap.js";
import getVideoMixInstance from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/VIdeoMix.js";
import strongExclusion from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/strongExclusion.js";
import FeatureEditor from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/featureEditor.js";
import LimitHeight from "https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/limitHeight.js";
import Polygon from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.26/@arcgis/core/geometry/Polygon.js";
import Graphic from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.26/@arcgis/core/Graphic.js";

let defaultExport = (window.ArcGisUtils = {
  Draw,
  initSceneView,
  initMapView,
  zoomIn,
  zoomOut,
  addHeatMap,
  addLineEffect,
  removeHeatmap,
  removeLineEffect,
  loadArcgisLayer,
  addLimitToView,
  removeViewLimit,
  // loadSceneLayer,
  loadWeatherLayer,
  removeWeatherLayer,
  addRoadLayer,
  removeRoadLayer,
  addWaterLayer,
  changeWaterLayer,
  RainfallAnalysis,
  removeWaterLayer,
  addIntegratedMesh,
  removeIntegratedMesh,
  createWeatherWidget,
  addWhiteModalLayer,
  removeWhiteModalLayer,
  loadPureModalLayer,
  removePureModalLayer,
  createAreaMeasurementWidget,
  createAreaMeasurement,
  MapPopupWidget,
  createSiceWidget,
  createSliceAnalysis,
  measuringDistance,
  createmMeasuringDistance,
  setXYPos,
  setCam,
  updateCam,
  addBimModalLayer,
  removeBimModalLayer,
  getSzLightBasemap,
  getTDTCGCS2000SYLayer,
  initializationView,
  exchangeMap,
  addWaterWaveEffect,

  DomAnalysis,
  addGeojsonToMap,
  load3DTextLayer, // 3D Text
  remove3DTextById,
  // Gif上图
  addGifLayer,
  removeGifLayer,
  createLineOfSightWidget, // 通视分析
  setGroundOpacity, // 设置地表透明度
  // 动态墙体
  addDynamicWall,
  removeDynamicWall,

  //
  addScanRadar,
  removeScanRadar,
  // 火焰效果
  addFireEffect,
  removeFireEffect,
  addFireSmokeEffect,
  removeFireSmokeEffect,
  addFountainEffect,
  removeFountainEffect,
  // 扩散圈
  addCylinderLayer,
  removeCylinderLayer,
  //
  ScatterToMap,
  // 三维柱体
  addBarLayer,
  removeBarLayer,
  addPyramidLayer,
  removePyramidLayer,
  addLineLayer,
  //视频融合
  addVideoToMap,
  removeVideosFromMap,
  // 雷达遮罩
  addRadarMask,
  removeRadarMask,
  addArrowLineLayer,
  change2DOr3D, // 二三维切换
  //  抬高面图层
  addExtrudeLayer,
  removeExtrudeLayer,
  addRoadVideoLayer, // 视频按线缓存结果图层
  removeRoadVideoLayer,
  // 路径图层
  addRouteLayer,
  removeRouteLayer,
  converPathToPoints,
  geodesic4490Areas,
  loadHeatmapLayer,
  createDaylightWidget,
  loadFlyLineLayer,
  queryTaskByGeometry,
  projectPoint,
  ClusterLayer,
  addOdLineEffect,
  removeOdLineEffect,
  //图层显隐
  setLayerVisible,
  //渐变白模
  addGRADIENTBAIMOLayer,
  removeGRADIENTBAIMOLayer,
  //路径规划
  addTiandituRoutePlanLayer,
  removeTiandituRoutePlanLayer,
  startRoadFlicker,
  pauseRoadFlicker,
  addTiandituRoutePlanLayer2,
  removeTiandituRoutePlanLayer2,
  queryLocationByAddress,
  //空间分析工具
  isGeometryContains,
  isGeometryTouch,
  isGeometryDisjointed,
  isGeometryIntersect,
  wktToArcGis,
  arcgisToWkt,
  createBuffer,
  //DEM
  addDEMToMap,
  removeDEMFromMap,
  //混合热力图
  addMixHeatMap,
  removeMixHeatMap,
  //白模分级渲染
  addClassBreakModalLayer,
  removeClassBreakModalLayer,
  //天际线
  Skyline,
  //缓冲区分析
  Buffer,
  //卷帘
  Swipe,
  //分屏
  Multiscreen,
  //二环内的热力图
  addheatMap2,
  removeHeatMap2,
  //二环，全量
  addMix3dHeatmap,
  removeMix3dHeatmap,
  //轨迹线
  TrackLayer,
  //轨迹
  RouteLayer,
  createBuildingExplorer,
  //自定义paths流光线
  addPathsLineEffect,
  removePathsLineEffect,
  //叠加分析
  PolygonBoolen,
  //点跟踪漫游
  VisualTracking,
  //环点漫游
  RoamByHeading,
  //自定义mesh
  MeshCreate,
  //航线漫游
  Fly,
  sketchExtrude,
  //加载gltf
  gltfModelToMap,
  //hex图层
  addHexGridLayer,
  //添加默认覆盖物
  addDefaultCustomLayer,
  queryVector,
  removeCodeLayer,
  //空间分析,
  SpatialAnalysis,
  //角度测量
  AngleMeasure,
  //网格特效
  addGridLayer,
  //poi显影
  changePoiLayerVisible,
  //全金华面
  addJinHuaCoverLayer,
  //自定义3d热力
  customHeatmap,
  //视频融合新
  getVideoMixInstance,
  //强排分析
  strongExclusion,
  //要素编辑
  FeatureEditor,
  //限高分析
  LimitHeight,
  Polygon,
  Graphic,
});

export default defaultExport;
