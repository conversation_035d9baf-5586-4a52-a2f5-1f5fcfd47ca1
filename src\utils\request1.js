import axios from 'axios'
import {  MessageBox } from 'element-ui'

const METHOD = {
  GET: 'get',
  POST: 'post',
  PUT: 'put',
}
// 创建 axios 实例
const service = axios.create({
  timeout: 60000,
  withCredentials: true,
})

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    const res = response.data
    // 这里可以根据后端约定的状态码进行统一处理
    if (res.code !== 200) {
      // 处理特定错误码
      if (res.code === 401) {
        MessageBox.alert('登录已过期，请重新登录', '提示', {
          confirmButtonText: '确定',
          callback: () => {
            localStorage.removeItem('Admin-Token')
            window.location.href = '/#/login'
          },
        })
        return Promise.reject(new Error('token已过期,请重新登录'))
      }
      return res
    }
    return res
  },
  (error) => {
    console.error('响应错误:', error)
    return Promise.reject(error)
  }
)

/**
 * axios请求
 * @param url 请求地址
 * @param method {METHOD} http method
 * @param params 请求参数
 * @returns {Promise<AxiosResponse<T>>}
 */
async function requestLogin1(data) {
  // 确保 URL 是安全的，避免注入攻击
  let url = `${process.env.VUE_APP_BASE_API}${data.url}`

  // 使用 async/await 语法简化代码
  try {
    let response
    // 使用 switch 语句选择请求方法
    switch (data.method) {
      case METHOD.GET:
        response = await service.get(url, { params: data.params })
        break
      case METHOD.POST:
        response = await service.post(url, data.data)
        break
      case METHOD.PUT:
        response = await service.put(url, data.data)
        break
      default:
        // 处理未知的请求方法
        throw new Error(`Unsupported request method: ${data.method}`)
    }

    // 返回标准化的响应对象
    return response
  } catch (error) {
    // 错误处理：可以根据需要记录错误日志或抛出简化后的错误信息
    console.error('Request failed:', error.message)
    // 可以选择向上抛出错误或返回错误信息
    throw error // 或者返回一个包含错误信息的对象
  }
}

export { requestLogin1 }
