<!--标绘-->
<template>
  <div class="container">
    <div class="container-content">
      <div class="container-content-item" :id="item.boxId" v-for="(item,i) in list" :key="i">
        <div class="container-content-item-top">
          <div class="container-content-item-name">{{item.name}}</div>
          <div class="container-content-item-btn" @click="drawFun(item,i)">{{item.status}}</div>
        </div>
        <div v-if="item.status =='关闭'" class="esri-widget" style="padding:12px" id="drawPoint-box-panel">{{item.desc}}</div>
      </div>
    </div>
    <div class="container-btn" @click="cleanall">移除标绘内容</div>
  </div>
</template>

<script>
export default {
  name: "Plot",
  data() {
    return {
      list: [
        {
          name:"绘点",
          status:"使用",
          desc:"请在地图上绘制点",
          type:"点"
        },
        {
          name:"绘线",
          status:"使用",
          desc:"请在地图上绘制线",
          type:"线"
        }
      ]
    }
  },
  computed: {},
  mounted() {

  },
  methods: {
    async drawFun(item,i) {
      let that = this
      if (item.status == '使用') {
        this.list[i].status = '关闭'
        let drawType = {
          点: { drawName: 'point', btnId: 'drawPoint-box', pnlId: 'drawPoint-box-panel', symbol: null },
          线: { drawName: 'polyline', btnId: 'drawPolyline-box', pnlId: 'drawPolyline-box-panel', symbol: null },
          面: { drawName: 'polygon', btnId: 'drawPolygon-box', pnlId: 'drawPolygon-box-panel', symbol: null },
          圆: { drawName: 'circle', btnId: 'drawCircle-box', pnlId: 'drawCircle-box-panel', symbol: null },
          矩形: { drawName: 'rectangle', btnId: 'drawRect-box', pnlId: 'drawRect-box-panel', symbol: null },
          虚线: {
            drawName: 'polyline',
            btnId: 'drawdotline-box',
            pnlId: 'drawdotline-box-panel',
            symbol: {
              color: [255, 0, 0],
              width: 5,
              style: 'dash',
              type: 'simple-line',
            },
          },
          条纹面: {
            drawName: 'polygon',
            btnId: 'drawlinePolygon-box',
            pnlId: 'drawlinePolygon-box-panel',
            symbol: {
              style: 'vertical',
              type: 'simple-fill',
              color: [255, 0, 0, 0.9],
              outline: {
                color: [255, 0, 0],
                width: 5,
              },
            },
          },
          X标记: {
            drawName: 'point',
            btnId: 'drawdotSignal-box',
            pnlId: 'drawdotSignal-box-panel',
            symbol: {
              color: [255, 255, 0],
              outline: {
                // autocasts as new SimpleLineSymbol()
                color: [255, 0, 0],
                width: 5,
              },
              size: 30,
              style: 'x',
              type: 'simple-marker',
            },
          },
          三角标记: {
            drawName: 'point',
            btnId: 'drawTriSignal-box',
            pnlId: 'drawTriSignal-box-panel',
            symbol: {
              color: [255, 0, 0],
              size: 30,
              style: 'triangle',
              type: 'simple-marker',
            },
          },
          方形标记: {
            drawName: 'point',
            btnId: 'drawCubicSignal-box',
            pnlId: 'drawCubicSignal-box-panel',
            symbol: {
              color: [255, 0, 0],
              size: 30,
              style: 'square',
              type: 'simple-marker',
            },
          },
          态势箭头: {
            drawName: 'general_arrow',
            btnId: 'drawArrow-box',
            pnlId: 'drawArrow-box-panel',
            symbol: null,
          },
          燕尾箭头: {
            drawName: 'swallowtail_arrow',
            btnId: 'drawSwallowTail-box',
            pnlId: 'drawSwallowTail-box-panel',
            symbol: null,
          },
        }
        top.window.drawTool?.cancel()
        if (!top.window.drawTool) top.window.drawTool = new top.ArcGisUtils.Draw({ view: top.mapUtil.mapview })
        top.window.drawTool.draw(drawType[item.type].drawName, drawType[item.type].symbol)
        if (item.type == '态势箭头' || item.type == '燕尾箭头') {
          while (1) {
            await top.window.drawTool.draw(drawType[item.type].drawName, drawType[item.type].symbol)
          }
        }
        if (item.type == '圆') {
          top.window.drawTool.sketchVM.on('create', function (e) {
            if (e.state == 'active') {
              let graphic = e.graphic
              let la1 = graphic.geometry.centroid.latitude
              let ln1 = graphic.geometry.centroid.longitude
              let la2 = graphic.geometry.rings[0][0][1]
              let ln2 = graphic.geometry.rings[0][0][0]

              // 调用 return的距离单位为km

              let radiu = that.GetDistance(la1, ln1, la2, ln2)
              let cent = { lat: la1, lng: ln1 }
              let are = Math.PI * Math.pow(radiu, 2)
              let circleresult = { radius: radiu, center: cent, area: are, coordinates: graphic.geometry.rings }
              that.list[i].desc = "当前圆半径为" + (Math.round(circleresult.radius * 100) / 100) + "公里";
            }
          })
        }
        if (item.type == '线') {
          top.window.drawTool.sketchVM.on('create', function (e) {
            if (e.state == 'active') {
              let graphic = e.graphic
              let totalLen = 0
              for (let i = 0; i < graphic.geometry.paths[0].length - 1; i++) {
                let la1 = graphic.geometry.paths[0][i][1]
                let ln1 = graphic.geometry.paths[0][i][0]
                let la2 = graphic.geometry.paths[0][i + 1][1]
                let ln2 = graphic.geometry.paths[0][i + 1][0]

                totalLen += that.GetDistance(la1, ln1, la2, ln2)
              }
              that.list[i].desc = "当前折线总长度为" + (Math.round(totalLen * 100) / 100) + "公里";
            }
          })
        }
      } else {
        this.list[i].status = '使用'
        this.cleanall()
      }
    },
    GetDistance(lat1, lng1, lat2, lng2) {
      let radLat1 = (lat1 * Math.PI) / 180.0
      let radLat2 = (lat2 * Math.PI) / 180.0
      let a = radLat1 - radLat2
      let b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0
      let s =
        2 *
        Math.asin(
          Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2))
        )
      s = s * 6378.137 // EARTH_RADIUS;
      s = Math.round(s * 10000) / 10000
      return s
    },
    cleanall() {
      top.window.drawTool?.destroy()
      top.window.drawTool = null
      this.list.forEach(item => {
        item.status = "使用"
      })
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    .container-content {
      height: 1000px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      overflow-y: scroll;
      .container-content-item {
        width: 400px;
        margin-bottom: 15px;
        .container-content-item-top {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .container-content-item-name {
            color: #fff;
            font-size: 30px;
          }
          .container-content-item-btn {
            width: 70px;
            height: 35px;
            cursor: pointer;
            border: 1px solid hsla(0, 0%, 100%, 0.30980392156862746);
            box-sizing: border-box;
            transition: 0.3s;
            line-height: 30px;
            font-size: 25px;
            background: linear-gradient(180deg, hsla(0, 0%, 100%, 0.4), hsla(0, 0%, 100%, 0));
            color: white;
            text-align: center;
          }
          .container-content-item-btn:hover {
            background: #2960cb;
            color: white;
          }
        }
      }
      .info-box {
        height: 100%;
        overflow-x: scroll;
        .info-ul {
          width: 100%;
          height: fit-content;
          margin-bottom: 20px;
        }
        .coordInfo-text {
          color: rgba(255, 255, 255, 0.6);
          font-size: 30px;
        }
      }
    }
    .container-btn {
      width: 400px;
      height: 35px;
      cursor: pointer;
      border: 1px solid hsla(0, 0%, 100%, 0.30980392156862746);
      box-sizing: border-box;
      transition: 0.3s;
      line-height: 30px;
      font-size: 25px;
      background: linear-gradient(180deg, hsla(0, 0%, 100%, 0.4), hsla(0, 0%, 100%, 0));
      color: white;
      text-align: center;
      position: absolute;
      bottom: 30px;
    }
    .container-btn:hover {
      background: #2960cb;
      color: white;
    }
}
</style>