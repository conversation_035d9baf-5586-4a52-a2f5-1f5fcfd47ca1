<template>
  <div>
    <iframe
      :src="url"
      frameborder="0"
      id="iframePhone"
      scrolling="no"
      allow="microphone;camera;"
      class="iframeContainer"
    ></iframe>
  </div>
</template>

<script>
export default {
  name: "callVideo",
  data() {
    return {
      url:""
    }
  },
  props: {
    number: {
      type: String,
      default: ''
    }
  },
  computed: {},
  mounted() {
    // 如果在挂载时number已经有值，可以在这里初始化url
    this.updateUrl(this.number);
  },
  methods: {
    updateUrl(number) {
      // 使用URLSearchParams来安全地构建查询参数
      const params = new URLSearchParams({
        access_type: 1,
        local_chatid: "JHCSDN001",
        volteIds: number
      });
      // 通过new URL()构造函数来确保URL的安全性和正确性
      const baseUrl = "https://wzywt.wzsafety.gov.cn/dispatch-ms/#/conference/single_url";
      const constructedUrl = new URL(baseUrl + "?" + params.toString());
      this.url = constructedUrl.toString();
    }
  },
  watch: {
    number(val) {
      // 当number发生变化时，更新url
      this.updateUrl(val);
    }
  }
}
</script>

<style scoped lang="less">
  .iframeContainer {
    width: 2960px;
    height: 1720px;
  }
</style>