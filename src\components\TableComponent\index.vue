<template>
  <div>
    <div class="table">
      <div class="table-header">
        <div class="th" v-for="item of thConfig" :key="item.field" :style="{ width: item.width }">{{ item.th }}</div>
      </div>
      <div class="table-body">
        <div class="tr" v-for="(item, index) of tableData" :class="index % 2 === 1 ? 'tr-bg' : ''" :key="item.id">
          <div
            class="td"
            v-html="item[thConfig[0].field]"
            :title="thConfig[0].hover ? item[thConfig[0].field] : ''"
          ></div>
          <div
            class="td"
            v-html="item[thConfig[1].field]"
            :title="thConfig[1].hover ? item[thConfig[1].field] : ''"
          ></div>
          <div class="td" v-html="item[thConfig[2].field]" :title="thConfig[2] ? item[thConfig[2].field] : ''"></div>
          <div
            class="td cursor"
            :style="{color:item['color']}"
            v-html="item[thConfig[3].field]"
            v-if="thConfig[3].field == 'jd'"
            @click="infoClick(item)"
          ></div>
          <div
            class="td"
            v-else
            v-html="item[thConfig[3].field]"
            :title="thConfig[3] ? item[thConfig[3].field] : ''"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    thConfig: {
      type: Array,
      default: [],
    },
    tableData: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {}
  },
  methods: {
    infoClick(item) {
      this.$emit('infoClick', item)
    },
  },
}
</script>

<style lang="less" scoped>
.table {
  .table-header {
    display: flex;
    width: 1049px;
    height: 62px;
    background: url('@/assets/eventTaskCenter/table-header-bg.png') 0px 30px no-repeat;
    background-size: 100% 100%;
    .th {
      width: 25%;
      color: #00f0ff;
      font-size: 32px;
      text-align: center;
      box-sizing: border-box;
      padding: 0 24px;
    }
  }
  .table-body {
    overflow-y: auto;
    height: 375px;
    .tr {
      display: flex;
      width: 1049px;
      height: 62px;
      .td {
        width: 25%;
        box-sizing: border-box;
        height: 68px;
        line-height: 68px;
        padding: 0 24px;
        text-align: center;
        font-size: 28px;
        color: #fff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .tr-bg {
      background: rgba(91, 180, 255, 0.2);
    }
  }
}
</style>
