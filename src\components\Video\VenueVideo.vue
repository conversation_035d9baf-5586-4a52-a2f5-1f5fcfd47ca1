<template>
  <div class="wrapper">
    <div :id="videoConfig.domId" :style="{ height: height, width: width }"></div>
  </div>
</template>

<script>
import DHWs from './DHWs.js'
const DHWsInstance = DHWs.getInstance({
  reConnectCount: 2,
  connectionTimeout: 30 * 1000000,
  messageEvents: {},
})

export default {
  name: 'venueVideo',
  components: {},
  props: {
    width: {
      type: String,
      default: '',
    },
    height: {
      type: String,
      default: '',
    },
    visible: {
      type: Boolean,
      default: true,
    },
    videoConfig: {
      type: Object,
      default: function () {
        return {}
      },
    },
    code: {
      type: String,
      default: '',
    },
    createflag: {
      type: Boolean,
      default: false,
    },
    destoryflag: {
      type: Boolean,
      default: false,
    },
    isLogin: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      ws: DHWsInstance,
    }
  },
  created() {},
  methods: {
    createMinitor() {
      this.ws.createCtrl([this.videoConfig])
    },
    changeVisible() {
      this.ws.setCtrlVisible([
        {
          ctrlCode: this.videoConfig.ctrlCode,
          visible: this.videoConfig.visible,
        },
      ])
    },
    //视频登陆
    loginVideo() {
      // let DHWsInstance = this.ws;
      const _this = this
      this.ws.detectConnectQt().then((res) => {
        if (res) {
          // 连接客户端成功
          this.ws.login({
            loginIp: '*************',
            loginPort: '7902',
            // userName: 'kfqywtg',
            // userPwd: 'kfq2024$',
            userName: 'csdn',
            userPwd: 'Jhcsdn2024$',
            token: '',
            https:1
          })
          this.ws.on('loginState', (res) => {
            if (res) {
              console.log('登录成功')
              _this.createMinitor()
            } else {
              console.log('登录失败')
            }
          })
        } else {
          // 连接客户端失败
          console.log('连接客户端失败')
        }
      })
    },
    //视频登出
    logOut() {
      this.ws.logout({
        loginIp: '*************',
      })
      console.log("登出")
    }
  },
  watch: {
    createflag(v) {
      if (v) {
        if (this.isLogin) {
          this.createMinitor()
        } else {
          this.loginVideo()
        }
      }
    },
    destoryflag(v) {
      if (v) {
        this.changeVisible()
      }
    },
    visible() {
      console.log("visible");
      this.changeVisible()
    }
  },
}
</script>

<style lang="less" scoped>
// .wrapper>div {
//   width: 1670px;
//   height: 739px;
// }
.wrapper {
  display: flex;
  justify-content: center;
  align-items: flex-start;
}
</style>
