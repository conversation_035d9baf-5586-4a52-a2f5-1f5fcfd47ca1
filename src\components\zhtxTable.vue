<template>
  <div class="table" :style="{width: width + 'px',height: (height+58) + 'px' }">
    <div class="table-title">
      <div class="table-cell titleFont" v-for="(item,i) in titleWithKey" :key="i" :style="{flex:item.flex}">{{item.label}}</div>
    </div>
    <div class="table-container">
      <div class="table-row" v-for="(item,i) in tableData" :key="i" @click="itemClick(item)">
        <div class="table-cell" v-for="(obj,j) in titleWithKey" :key="j" :style="{flex:obj.flex}">
          <div class="cell-inner" :title="item[titleWithKey[j].key]" :style="getItemColorClass(item, obj)">
            {{item[titleWithKey[j].key]}}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "zhtxTable",
  props: {
    tableData: {
      type: Array,
      default: () => {
        return []
      }
    },
    titleWithKey: {
      type: Array,
      default: () => {
        return []
      }
    },
    width: {
      type: Number,
      default: 940
    },
    height: {
      type: Number,
      default: 362
    }
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {

  },
  methods: {
    getItemColorClass(item,obj) {
      if (obj.isColor && item[obj.key + 'Color']) {
        return { color: item[obj.key + 'Color'] };
      } else {
        return { color: 'white' };
      }
    },
    itemClick(item) {
      this.$emit("rowClick",item)
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .table {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    .table-title {
      width: 100%;
      height: 50px !important;
      background: #00396F;
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
    .titleFont {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 30px;
      color: #77B3F1 !important;
    }
    .table-cell {
      flex: 1;
      height: 100%;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 28px;
      color: #FFFFFF;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      .cell-inner {
        text-align: center;
        margin: 14px 0 13px 23px;
        height: 60px;
        line-height: 60px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    ::-webkit-scrollbar {
      width: 0;
    }
    .table-container {
      width: 100%;
      height: 100%;
      overflow-y: scroll;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      .table-row {
        width: 100%;
        height: 100px;
        background: #0F2B4D;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-top: 8px;
        cursor: pointer;
      }
    }
  }
</style>