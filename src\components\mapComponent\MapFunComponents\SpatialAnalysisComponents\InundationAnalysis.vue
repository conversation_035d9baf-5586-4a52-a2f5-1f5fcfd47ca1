<!--淹没分析-->
<template>
  <div class="panelContainer">
    <div class="btn back" @click="back">返回</div>
    <div class="btn func" id="floodRegion" @click="floodSimulate">绘制分析区域</div>
    <div class="btn func" id="floodclear" @click="floodSimulate">清除</div>
    <div id="floodArea"></div>
  </div>
</template>

<script>
import {csdnAreaCreate, csdnAuth, csdnMapYmfx, solrSearch} from "@/api/csdnIndexApi";
export default {
  name: "InundationAnalysis",
  data() {
    return {
      tableData: [
        {
          name: '医院',
          code: '医院',
          icon: '医院c',
          num: '',
          unit: '家',
          avg: '',
          arr: [],
        },
        {
          name: '学校',
          icon: '学校c',
          num: '',
          code: '学校',
          unit: '所',
          avg: '',
          arr: [],
        },
        {
          name: '公交车站',
          code: '公交站',
          icon: '公交车站c',
          num: '',
          unit: '个',
          avg: '',
          arr: [],
        },
        {
          name: '公共厕所',
          code: '公厕',
          icon: '公共厕所c',
          num: '',
          unit: '个',
          avg: '',
          arr: [],
        },
        {
          name: '运动场馆',
          code: '游泳场池,高尔夫球场,场馆',
          icon: '运动场馆c',
          num: '',
          unit: '个',
          avg: '',
          arr: [],
        },
        {
          name: '公园广场',
          code: '植物园,公园,广场,动物园',
          icon: '公园广场c',
          num: '',
          unit: '个',
          avg: '',
          arr: [],
        },
      ],
    }
  },
  computed: {},
  mounted() {
    console.log("creditAuth");
    this.creditAuth()
  },
  methods: {
    creditAuth() {
      console.log("creditAuth");
      var appKey = 'zj_jh-API',
        masterSecret = 'vqVINnikQoL8fHbfgLzdzC62'

      var timestamp = Number(Math.round(new Date().getTime() / 1000).toString())

      var time_md5 = CryptoJS.MD5(timestamp.toString()).toString()

      var o_str = appKey + time_md5 + masterSecret,
        sha256_str = CryptoJS.SHA256(o_str).toString()

      var sign = sha256_str + masterSecret

      const reqParams = {
        appKey: appKey,
        sign: sign,
        timestamp: timestamp,
        version: 'v1.0',
      }

      return csdnAuth(reqParams).then(function (res) {
        if (res.data.errno === 0) {
          window.accessToken = res.data.data.accessToken
        }
      })
    },
    back() {
      this.$emit('back')
    },
    floodSimulate() {
      let view = top.window.mapUtil.mapview
      if (top.window.rainFail) {
        top.window.rainFail.stop()
        top.window.rainFail = null
        document.getElementById('floodArea').innerHTML = ``
      }
      top.window.rainFail = new top.ArcGisUtils.RainfallAnalysis({ view })
      top.window.rainFail.addWaterRenderLayer(view).then((res) => {
        if (res.rings.length > 1) {
          document.getElementById('floodArea').innerHTML = `<p>绘制面不可交叉，请重新绘制！</p>`
          return
        }
        let geojson = { geometry: top.ArcgisToGeojsonUtils.arcgisToGeoJSON(res) }

        this.createWLGZ(geojson)

        top.window.rainFail.getWaterBaseSurface().then((value) => {
          view.environment.weather = {
            type: 'rainy', // autocasts as new RainyWeather({ cloudCover: 0.7, precipitation: 0.3 })
            cloudCover: 0.7,
            precipitation: 0.3,
          }
          setTimeout(() => {
            top.window.rainFail.setWaterElevationOffset(value * 1)
            top.window.rainFail.setRainfall(20)
            top.window.rainFail.start()
          }, 3000)
        })
      })
    },
    createWLGZ(faceMap) {
      let this_ = this
      // 创建区域
      if (faceMap.geometry.coordinates[0] != []) {
        let str = ''
        faceMap.geometry.coordinates[0].forEach((ele) => {
          str += ele[0] + ',' + ele[1] + '|'
        })
        let faceStr = str.slice(0, str.length - 1)
        csdnAreaCreate({
          shape: faceStr,
          precision: 7,
        }).then(function (res) {
          this_.getCount(res.data, faceMap)
        })
      }
    },
    getCount(id, faceMap) {
      let this_ = this
      let d = new Date()
      let start = `${d.getFullYear()}-${d.getMonth() + 1}-${d.getDate()} ${d.getHours().toString().padStart(2, '0')}:${d
        .getMinutes()
        .toString()
        .padStart(2, '0')}:${d.getSeconds().toString().padStart(2, '0')}`
      var frontOneHour = new Date(d.getTime() - 3 * 60 * 60 * 1000)
      let end = `${frontOneHour.getFullYear()}-${frontOneHour.getMonth() + 1}-${frontOneHour.getDate()} ${frontOneHour
        .getHours()
        .toString()
        .padStart(2, '0')}:${frontOneHour.getMinutes().toString().padStart(2, '0')}:${frontOneHour
        .getSeconds()
        .toString()
        .padStart(2, '0')}`

        csdnMapYmfx({
          id: id.data,
          type: 2,
          start_time: end,
          end_time: start,
        }).then(async (res) => {
          //此处需要异步获取，否则总是第二次访问时才获取到
          let person_count_data = { mapGeoJson: faceMap, countAll: res.data.data }
          this_.serchFun(person_count_data)
          console.log(
            `淹没面积：${(top.turf.area(faceMap.geometry) / 1000000).toFixed(2)}km²影响人数：${res.data.data}人`
          )
        })

    },
    async serchFun(data) {
      let obj = { type: 'FeatureCollection', features: [data.mapGeoJson] } //暂时注释、21日接口问题
      document.getElementById('floodArea').innerHTML = `<div>淹没面积：${(top.turf.area(data.mapGeoJson.geometry) / 1000000).toFixed(2)}km²</div><div>影响人数：${data.countAll}人</div>`;
      this.fetchData(obj);
    },

    async fetchData(obj) {
      const promises = this.tableData.map((item, index) => {
        return this.fetchSingleData(index, item, obj);
      });

      try {
        await Promise.all(promises);
        this.updateDOM();
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    },
    async fetchSingleData(index, item, obj) {
      const url = `${process.env.VUE_APP_BASE_API}${process.env.VUE_APP_BASE_API_MAP}/api2.0/solr-provider/api/data-sources/solr-search`;

      try {
        const response = await solrSearch({
          pageInfo: {
            current: 1,
            size: 100,
            totalSize: 0,
          },
          text: item.code,
          tableNames: 'poi',
          returnGeo: true,
          sortField: '',
          order: '',
          geometry: JSON.stringify(obj), // 确保obj是安全的
        })

        item.arr = response.data.data;
        item.num = response.data.pageInfo.totalSize;

        // 仅用于日志记录，生产环境中可采用更合适的日志方案
        console.log(`${item.name}: ${item.num}${item.unit}`);
      } catch (error) {
        console.error(`Error fetching data for ${item.name}:`, error);
      }
    },
    updateDOM() {
      // 这里避免了多次操作DOM
      const fragment = document.createDocumentFragment();
      this.tableData.forEach(item => {
        const element = document.createElement('div');
        element.textContent = `${item.name}: ${item.num}${item.unit}`;
        fragment.appendChild(element);
      });
      document.getElementById('floodArea').appendChild(fragment);
    },
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .panelContainer {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    #floodArea {
      color: white;
      font-size: 30px;
      margin-top: 20px;
    }
    .btn {
      cursor: pointer;
      border: 1px solid hsla(0, 0%, 100%, 0.30980392156862746);
      box-sizing: border-box;
      transition: 0.3s;
      line-height: 30px;
      font-size: 25px;
      background: linear-gradient(180deg, hsla(0, 0%, 100%, 0.4), hsla(0, 0%, 100%, 0));
      color: white;
      text-align: center;
    }
    .back {
      width: 70px;
      height: 35px;
    }
    .func {
      width: 400px;
      height: 35px;
      margin-top: 15px;
    }
  }
</style>