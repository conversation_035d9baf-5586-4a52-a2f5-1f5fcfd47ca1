import {request} from '@/utils/request'

// 物联平台通用接口
export function getIotInterface(data) {
  return request({
    url: '/ywtg-api/wlpt/getParameter',
    method: 'post',
    data
  })
}

// 查询产品列表
export function getPkList() {
  return request({
    url: '/ywtg-api/kfqywtg/pk/dpList',
    method: 'get'
  })
}

// 查询设备列表
export function getDeviceList(params) {
  return request({
    url: '/ywtg-api/kfqywtg/ywtgYjSb/dpList',
    method: 'get',
    params
  })
}