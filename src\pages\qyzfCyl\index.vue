<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-04-14 11:38:22
 * @LastEditors: wjb
 * @LastEditTime: 2025-06-11 15:25:44
-->
<template>
  <div class="qyzf-container">
    <div class="inneLeft">
      <wrapbox bg="left">
        <cylLeft class="animate__animated animate__fadeInLeft" />
      </wrapbox>
    </div>
    <div class="midBottom animate__animated animate__fadeIn">
      <cylCenter />
    </div>
    <div class="innerRight">
      <wrapbox bg="right">
        <cylRight class="animate__animated animate__fadeInLeft" />
      </wrapbox>
    </div>
  </div>
</template>

<script>
import wrapbox from '@/components/wrapbox'
import cylLeft from '@/pages/qyzfCyl/components/cylLeft'
import cylCenter from '@/pages/qyzfCyl/components/cylCenter'
import cylRight from '@/pages/qyzfCyl/components/cylRight'
export default {
  name: 'index',
  data() {
    return {
      visible: false,
      qyInfoVisible: false,
      qylx: '',
      allMessage: [],
    }
  },
  components: {
    wrapbox,
    cylLeft,
    cylCenter,
    cylRight,
  },
  computed: {},
  mounted() {},
  methods: {},
  watch: {},
}
</script>

<style scoped lang="less">
.qyzf-container {
  position: relative;
  width: 100%;
  height: 100%;
  // z-index: 2;
  // background: url('~@/assets/application/dtBg.png') no-repeat center center;
  // background-size: 100% 100%;
  .inneLeft {
    position: absolute;
    top: 229px;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }
  .innerRight {
    position: absolute;
    top: 229px;
    right: 0;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }
  .midBottom {
    position: absolute;
    left: 2135px;
    top: 229px;
    z-index: 2;
  }
}
</style>
