<!--
 * @Description:
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2024-05-07 16:53:05
 * @LastEditors: wjb
 * @LastEditTime: 2024-05-08 08:29:17
-->
<template>
  <div class="tab_box">
    <div
      class="tab_item"
      :class="{ on: tabIdx == index }"
      v-for="(item, index) in tabList"
      :key="index"
      @click="titleClick(index)"
    >
      <div :class="tabIdx == index ? 'tab_on' : 'tab'">{{ item }}</div>
      <img v-if='tabIdx == index' src="../assets/common/tab_on.png" alt="" class="tab_on_bkg">
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tabIdx: this.value
    }
  },
  props: {
    tabList: {
      type: Array,
      default: () => [],
    },
    value: {
      type: Number,
      default: 0,
    }
  },
  computed: {},
  mounted() {},
  methods: {
    titleClick(idx) {
      this.tabIdx = idx
      this.$emit("input", idx)
      this.$emit('titleClick', idx)
    },
  },
  watch: {},
}
</script>

<style scoped lang="less">
.tab_box {
  display: flex;
  align-content: center;
  align-items: center;
  .tab_item {
    position: relative;
    margin-right: 46px;
    cursor: pointer;
    .tab {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 32px;
      color: #d1d6df;
      line-height: 64px;
      font-style: italic;
      background: linear-gradient(0deg, #acddff 0%, #ffffff 100%);
      opacity: 0.57;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .tab_on {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 32px;
      color: #ffffff;
      line-height: 64px;
      font-style: italic;
    }
    .tab_on_bkg {
      width: 155px;
      height: 47px;
      position: absolute;
      left: 0;
      bottom: 0;
      z-index: -1;
    }
  }
}
</style>
